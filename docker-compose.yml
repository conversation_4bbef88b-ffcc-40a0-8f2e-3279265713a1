version: '3.8'

services:
  web:
    build: .
    container_name: silverstripe_web
    ports:
      - "8080:80"
    volumes:
      - ./app:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
    environment:
      - SS_DATABASE_SERVER=db
      - SS_DATABASE_NAME=silverstripe
      - SS_DATABASE_USERNAME=silverstripe
      - SS_DATABASE_PASSWORD=silverstripe
      - SS_ENVIRONMENT_TYPE=dev
      - SS_DEFAULT_ADMIN_USERNAME=admin
      - SS_DEFAULT_ADMIN_PASSWORD=admin123
    depends_on:
      - db
    networks:
      - silverstripe-network

  db:
    image: mysql:8.0
    container_name: silverstripe_db
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: silverstripe
      MYSQL_USER: silverstripe
      MYSQL_PASSWORD: silverstripe
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - silverstripe-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: silverstripe_phpmyadmin
    ports:
      - "8081:80"
    environment:
      PMA_HOST: db
      PMA_USER: silverstripe
      PMA_PASSWORD: silverstripe
    depends_on:
      - db
    networks:
      - silverstripe-network

volumes:
  db_data:

networks:
  silverstripe-network:
    driver: bridge
