<!--

Configuration to whitelist appropriate asset files, for IIS.
Note that you will need to maintain this whitelist yourself if you modify File::$allowed_extensions

If you are not using IIS then you can ignore this file.
If you are using Apache then you should look at assets/.htaccess instead.

To add an extension to to the list, you will need to add another <add> entry inside the <fileExtensions>
tag.

For example, to add *.exe files to the list of downloadable assets, add this line below the
<fileExtensions line>:

    <add fileExtension=".exe" allowed="true" />

Once you do this, visitors will be able to download *.exe files that are uploaded to the assets
directory.

-->
<configuration>
	<system.webServer>
		<security>
			<requestFiltering>
				<fileExtensions allowUnlisted="false" applyToWebDAV="true">
					<add fileExtension=".html" allowed="true" />
					<add fileExtension=".htm" allowed="true" />
					<add fileExtension=".xhtml" allowed="true" />
					<add fileExtension=".js" allowed="true" />
					<add fileExtension=".css" allowed="true" />
					<add fileExtension=".bmp" allowed="true" />
					<add fileExtension=".png" allowed="true" />
					<add fileExtension=".gif" allowed="true" />
					<add fileExtension=".jpg" allowed="true" />
					<add fileExtension=".jpeg" allowed="true" />
					<add fileExtension=".ico" allowed="true" />
					<add fileExtension=".pcx" allowed="true" />
					<add fileExtension=".tif" allowed="true" />
					<add fileExtension=".tiff" allowed="true" />
					<add fileExtension=".au" allowed="true" />
					<add fileExtension=".mid" allowed="true" />
					<add fileExtension=".midi" allowed="true" />
					<add fileExtension=".mpa" allowed="true" />
					<add fileExtension=".mp3" allowed="true" />
					<add fileExtension=".ogg" allowed="true" />
					<add fileExtension=".m4a" allowed="true" />
					<add fileExtension=".ra" allowed="true" />
					<add fileExtension=".wma" allowed="true" />
					<add fileExtension=".wav" allowed="true" />
					<add fileExtension=".cda" allowed="true" />
					<add fileExtension=".avi" allowed="true" />
					<add fileExtension=".mpg" allowed="true" />
					<add fileExtension=".mpeg" allowed="true" />
					<add fileExtension=".asf" allowed="true" />
					<add fileExtension=".wmv" allowed="true" />
					<add fileExtension=".m4v" allowed="true" />
					<add fileExtension=".mov" allowed="true" />
					<add fileExtension=".mkv" allowed="true" />
					<add fileExtension=".mp4" allowed="true" />
					<add fileExtension=".ogv" allowed="true" />
					<add fileExtension=".webm" allowed="true" />
					<add fileExtension=".swf" allowed="true" />
					<add fileExtension=".flv" allowed="true" />
					<add fileExtension=".ram" allowed="true" />
					<add fileExtension=".rm" allowed="true" />
					<add fileExtension=".doc" allowed="true" />
					<add fileExtension=".docx" allowed="true" />
					<add fileExtension=".txt" allowed="true" />
					<add fileExtension=".rtf" allowed="true" />
					<add fileExtension=".xls" allowed="true" />
					<add fileExtension=".xlsx" allowed="true" />
					<add fileExtension=".pages" allowed="true" />
					<add fileExtension=".ppt" allowed="true" />
					<add fileExtension=".pptx" allowed="true" />
					<add fileExtension=".pps" allowed="true" />
					<add fileExtension=".csv" allowed="true" />
					<add fileExtension=".cab" allowed="true" />
					<add fileExtension=".arj" allowed="true" />
					<add fileExtension=".tar" allowed="true" />
					<add fileExtension=".zip" allowed="true" />
					<add fileExtension=".zipx" allowed="true" />
					<add fileExtension=".sit" allowed="true" />
					<add fileExtension=".sitx" allowed="true" />
					<add fileExtension=".gz" allowed="true" />
					<add fileExtension=".tgz" allowed="true" />
					<add fileExtension=".bz2" allowed="true" />
					<add fileExtension=".ace" allowed="true" />
					<add fileExtension=".arc" allowed="true" />
					<add fileExtension=".pkg" allowed="true" />
					<add fileExtension=".dmg" allowed="true" />
					<add fileExtension=".hqx" allowed="true" />
					<add fileExtension=".jar" allowed="true" />
					<add fileExtension=".xml" allowed="true" />
					<add fileExtension=".pdf" allowed="true" />
					<add fileExtension=".gpx" allowed="true" />
					<add fileExtension=".kml" allowed="true" />
				</fileExtensions>
			</requestFiltering>
		</security>
	</system.webServer>
</configuration>
