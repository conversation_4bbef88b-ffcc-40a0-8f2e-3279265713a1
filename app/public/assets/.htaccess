#
# Whitelist appropriate assets files.
# This file is automatically generated via File.allowed_extensions configuration
# See AssetAdapter::renderTemplate() for reference.
#

# We disable P<PERSON> via several methods
# Replace the handler with the default plaintext handler
<PERSON>d<PERSON><PERSON>ler default-handler php phtml php3 php4 php5 inc

<IfModule mod_php5.c>
    # Turn the PHP engine off
    php_flag engine off
</IfModule>

<IfModule mod_rewrite.c>
    <IfModule mod_env.c>
        SetEnv HTTP_MOD_REWRITE On
    </IfModule>

    RewriteEngine On

    # Allow error pages
    RewriteCond %{REQUEST_FILENAME} -f
    RewriteRule error[^\\/]*\.html$ - [L]

    # Block invalid file extensions
    RewriteCond %{REQUEST_URI} !^[^.]*\.(?i:css|js|ace|arc|arj|asf|au|avi|bmp|bz2|cab|cda|csv|dmg|doc|docx|dotx|flv|gif|gpx|gz|hqx|ico|jpeg|jpg|kml|m4a|m4v|mid|midi|mkv|mov|mp3|mp4|mpa|mpeg|mpg|ogg|ogv|pages|pcx|pdf|png|pps|ppt|pptx|potx|ra|ram|rm|rtf|sit|sitx|tar|tgz|tif|tiff|txt|wav|webm|wma|wmv|xls|xlsx|xltx|zip|zipx)$
    RewriteRule .* - [F]

    # Non-existent files passed to requesthandler
    RewriteCond %{REQUEST_URI} ^(.*)$
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule .* ../index.php [QSA]
</IfModule>
