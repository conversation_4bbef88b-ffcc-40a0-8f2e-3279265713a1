{"name": "embed/embed", "type": "library", "description": "PHP library to retrieve page info using oembed, opengraph, etc", "keywords": ["oembed", "opengraph", "twitter cards", "embed", "embedly"], "homepage": "https://github.com/oscarotero/Embed", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/oscarotero/Embed/issues"}, "require": {"php": "^7.4|^8", "ext-curl": "*", "ext-dom": "*", "ext-json": "*", "ext-mbstring": "*", "composer/ca-bundle": "^1.0", "oscarotero/html-parser": "^0.1.4", "psr/http-message": "^1.0|^2.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "ml/json-ld": "^1.1"}, "require-dev": {"phpunit/phpunit": "^9.0", "friendsofphp/php-cs-fixer": "^2.0", "nyholm/psr7": "^1.2", "oscarotero/php-cs-fixer-config": "^1.0", "brick/varexporter": "^0.3.1", "symfony/css-selector": "^5.0"}, "suggest": {"symfony/css-selector": "If you want to get elements using css selectors"}, "autoload": {"psr-4": {"Embed\\": "src"}, "files": ["src/functions.php"]}, "autoload-dev": {"psr-4": {"Embed\\Tests\\": "tests/"}}, "scripts": {"demo": "php -S localhost:8888 demo/index.php", "test": "phpunit", "cs-fix": "php-cs-fixer fix", "update-resources": ["php scripts/update-oembed.php", "php scripts/update-suffix.php"]}}