{"name": "webonyx/graphql-php", "description": "A PHP port of GraphQL reference implementation", "license": "MIT", "type": "library", "keywords": ["graphql", "API"], "homepage": "https://github.com/webonyx/graphql-php", "require": {"php": "^7.4 || ^8", "ext-json": "*", "ext-mbstring": "*"}, "require-dev": {"amphp/amp": "^2.6", "amphp/http-server": "^2.1", "dms/phpunit-arraysubset-asserts": "dev-master", "ergebnis/composer-normalize": "^2.28", "friendsofphp/php-cs-fixer": "3.82.2", "mll-lab/php-cs-fixer-config": "5.11.0", "nyholm/psr7": "^1.5", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "2.1.17", "phpstan/phpstan-phpunit": "2.0.6", "phpstan/phpstan-strict-rules": "2.0.4", "phpunit/phpunit": "^9.5 || ^10.5.21 || ^11", "psr/http-message": "^1 || ^2", "react/http": "^1.6", "react/promise": "^2.0 || ^3.0", "rector/rector": "^2.0", "symfony/polyfill-php81": "^1.23", "symfony/var-exporter": "^5 || ^6 || ^7", "thecodingmachine/safe": "^1.3 || ^2 || ^3"}, "suggest": {"amphp/http-server": "To leverage async resolving with webserver on AMPHP platform", "psr/http-message": "To use standard GraphQL server", "react/promise": "To leverage async resolving on React PHP platform"}, "autoload": {"psr-4": {"GraphQL\\": "src/"}}, "autoload-dev": {"psr-4": {"GraphQL\\Benchmarks\\": "benchmarks/", "GraphQL\\Examples\\Blog\\": "examples/01-blog/Blog/", "GraphQL\\Tests\\": "tests/"}}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "ergebnis/composer-normalize": true, "phpstan/extension-installer": true}, "preferred-install": "dist", "sort-packages": true}, "scripts": {"baseline": "phpstan --generate-baseline", "bench": "phpbench run", "check": ["@fix", "@stan", "@test"], "docs": "php generate-class-reference.php", "fix": ["@rector", "@php-cs-fixer"], "php-cs-fixer": "php-cs-fixer fix", "rector": "rector process", "stan": "phpstan --verbose", "test": "php -d zend.exception_ignore_args=Off -d zend.assertions=On -d assert.active=On -d assert.exception=On vendor/bin/phpunit"}}