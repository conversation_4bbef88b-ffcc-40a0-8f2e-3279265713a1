{"packages": [{"name": "composer/ca-bundle", "version": "1.5.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "d665d22c417056996c59019579f1967dfe5c1e82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/d665d22c417056996c59019579f1967dfe5c1e82", "reference": "d665d22c417056996c59019579f1967dfe5c1e82", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8 || ^9", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/process": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "time": "2025-05-26T15:08:54+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.5.7"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./ca-bundle"}, {"name": "composer/installers", "version": "v2.3.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "12fb2dfe5e16183de69e784a7b84046c43d97e8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/12fb2dfe5e16183de69e784a7b84046c43d97e8e", "reference": "12fb2dfe5e16183de69e784a7b84046c43d97e8e", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": "^7.2 || ^8.0"}, "require-dev": {"composer/composer": "^1.10.27 || ^2.7", "composer/semver": "^1.7.2 || ^3.4.0", "phpstan/phpstan": "^1.11", "phpstan/phpstan-phpunit": "^1", "symfony/phpunit-bridge": "^7.1.1", "symfony/process": "^5 || ^6 || ^7"}, "time": "2024-06-24T20:46:46+00:00", "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "2.x-dev"}, "plugin-modifies-install-path": true}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "concreteCMS", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "matomo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "tastyigniter", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v2.3.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./installers"}, {"name": "composer/semver", "version": "3.4.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "time": "2024-09-19T14:15:21+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./semver"}, {"name": "doctrine/lexer", "version": "3.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "time": "2024-02-05T11:56:58+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "install-path": "../doctrine/lexer"}, {"name": "egulias/email-validator", "version": "4.0.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "time": "2025-03-06T22:45:56+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "install-path": "../egulias/email-validator"}, {"name": "embed/embed", "version": "v4.4.17", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/php-embed/Embed.git", "reference": "b2ea091a5586c14ea5f2c5bf52fb0ef38e5aef87"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-embed/Embed/zipball/b2ea091a5586c14ea5f2c5bf52fb0ef38e5aef87", "reference": "b2ea091a5586c14ea5f2c5bf52fb0ef38e5aef87", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "ext-curl": "*", "ext-dom": "*", "ext-json": "*", "ext-mbstring": "*", "ml/json-ld": "^1.1", "oscarotero/html-parser": "^0.1.4", "php": "^7.4|^8", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0|^2.0"}, "require-dev": {"brick/varexporter": "^0.3.1", "friendsofphp/php-cs-fixer": "^2.0", "nyholm/psr7": "^1.2", "oscarotero/php-cs-fixer-config": "^1.0", "phpunit/phpunit": "^9.0", "symfony/css-selector": "^5.0"}, "suggest": {"symfony/css-selector": "If you want to get elements using css selectors"}, "time": "2025-05-13T12:42:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/functions.php"], "psr-4": {"Embed\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "PHP library to retrieve page info using oembed, opengraph, etc", "homepage": "https://github.com/oscarotero/Embed", "keywords": ["embed", "embedly", "oembed", "opengraph", "twitter cards"], "support": {"email": "<EMAIL>", "issues": "https://github.com/oscarotero/Embed/issues", "source": "https://github.com/php-embed/Embed/tree/v4.4.17"}, "funding": [{"url": "https://paypal.me/oscarotero", "type": "custom"}, {"url": "https://github.com/oscarotero", "type": "github"}, {"url": "https://www.patreon.com/misteroom", "type": "patreon"}], "install-path": "../embed/embed"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "time": "2025-03-27T13:37:11+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "install-path": "../guzzlehttp/guzzle"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "time": "2025-03-27T13:27:01+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "install-path": "../guzzlehttp/promises"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2025-03-27T12:30:47+00:00", "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "install-path": "../guzzlehttp/psr7"}, {"name": "intervention/image", "version": "2.7.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "04be355f8d6734c826045d02a1079ad658322dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/04be355f8d6734c826045d02a1079ad658322dad", "reference": "04be355f8d6734c826045d02a1079ad658322dad", "shasum": ""}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1 || ^2.0", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7 || ^7.5.15"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "time": "2022-05-21T17:30:32+00:00", "type": "library", "extra": {"laravel": {"aliases": {"Image": "Intervention\\Image\\Facades\\Image"}, "providers": ["Intervention\\Image\\ImageServiceProvider"]}, "branch-alias": {"dev-master": "2.4-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "support": {"issues": "https://github.com/Intervention/image/issues", "source": "https://github.com/Intervention/image/tree/2.7.2"}, "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}], "install-path": "../intervention/image"}, {"name": "league/csv", "version": "9.24.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/thephpleague/csv.git", "reference": "e0221a3f16aa2a823047d59fab5809d552e29bc8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/csv/zipball/e0221a3f16aa2a823047d59fab5809d552e29bc8", "reference": "e0221a3f16aa2a823047d59fab5809d552e29bc8", "shasum": ""}, "require": {"ext-filter": "*", "php": "^8.1.2"}, "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "friendsofphp/php-cs-fixer": "^3.75.0", "phpbench/phpbench": "^1.4.1", "phpstan/phpstan": "^1.12.27", "phpstan/phpstan-deprecation-rules": "^1.2.1", "phpstan/phpstan-phpunit": "^1.4.2", "phpstan/phpstan-strict-rules": "^1.6.2", "phpunit/phpunit": "^10.5.16 || ^11.5.22", "symfony/var-dumper": "^6.4.8 || ^7.3.0"}, "suggest": {"ext-dom": "Required to use the XMLConverter and the HTMLConverter classes", "ext-iconv": "Needed to ease transcoding CSV using iconv stream filters", "ext-mbstring": "Needed to ease transcoding CSV using mb stream filters", "ext-mysqli": "Requiered to use the package with the MySQLi extension", "ext-pdo": "Required to use the package with the PDO extension", "ext-pgsql": "Requiered to use the package with the PgSQL extension", "ext-sqlite3": "Required to use the package with the SQLite3 extension"}, "time": "2025-06-25T14:53:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Csv\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "description": "CSV data manipulation made easy in PHP", "homepage": "https://csv.thephpleague.com", "keywords": ["convert", "csv", "export", "filter", "import", "read", "transform", "write"], "support": {"docs": "https://csv.thephpleague.com", "issues": "https://github.com/thephpleague/csv/issues", "rss": "https://github.com/thephpleague/csv/releases.atom", "source": "https://github.com/thephpleague/csv"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "install-path": "../league/csv"}, {"name": "league/flysystem", "version": "3.30.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "2203e3151755d874bb2943649dae1eb8533ac93e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/2203e3151755d874bb2943649dae1eb8533ac93e", "reference": "2203e3151755d874bb2943649dae1eb8533ac93e", "shasum": ""}, "require": {"league/flysystem-local": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "conflict": {"async-aws/core": "<1.19.0", "async-aws/s3": "<1.14.0", "aws/aws-sdk-php": "3.209.31 || 3.210.0", "guzzlehttp/guzzle": "<7.0", "guzzlehttp/ringphp": "<1.1.1", "phpseclib/phpseclib": "3.0.15", "symfony/http-client": "<5.2"}, "require-dev": {"async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "aws/aws-sdk-php": "^3.295.10", "composer/semver": "^3.0", "ext-fileinfo": "*", "ext-ftp": "*", "ext-mongodb": "^1.3|^2", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "guzzlehttp/psr7": "^2.6", "microsoft/azure-storage-blob": "^1.1", "mongodb/mongodb": "^1.2|^2", "phpseclib/phpseclib": "^3.0.36", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5.11|^10.0", "sabre/dav": "^4.6.0"}, "time": "2025-06-25T13:29:59+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"League\\Flysystem\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "File storage abstraction for PHP", "keywords": ["WebDAV", "aws", "cloud", "file", "files", "filesystem", "filesystems", "ftp", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.30.0"}, "install-path": "../league/flysystem"}, {"name": "league/flysystem-local", "version": "3.30.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-local.git", "reference": "6691915f77c7fb69adfb87dcd550052dc184ee10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/6691915f77c7fb69adfb87dcd550052dc184ee10", "reference": "6691915f77c7fb69adfb87dcd550052dc184ee10", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/flysystem": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "time": "2025-05-21T10:34:19+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"League\\Flysystem\\Local\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Local filesystem adapter for Flysystem.", "keywords": ["Flysystem", "file", "files", "filesystem", "local"], "support": {"source": "https://github.com/thephpleague/flysystem-local/tree/3.30.0"}, "install-path": "../league/flysystem-local"}, {"name": "league/mime-type-detection", "version": "1.16.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/2d6702ff215bf922936ccc1ad31007edc76451b9", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "time": "2024-09-21T08:32:55+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.16.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "install-path": "../league/mime-type-detection"}, {"name": "m1/env", "version": "2.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/m1/Env.git", "reference": "5c296e3e13450a207e12b343f3af1d7ab569f6f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/m1/Env/zipball/5c296e3e13450a207e12b343f3af1d7ab569f6f3", "reference": "5c296e3e13450a207e12b343f3af1d7ab569f6f3", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.*", "scrutinizer/ocular": "~1.1", "squizlabs/php_codesniffer": "^2.3"}, "suggest": {"josegonzalez/dotenv": "For loading of .env", "m1/vars": "For loading of configs"}, "time": "2020-02-19T09:02:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"M1\\Env\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://milescroxford.com", "role": "Developer"}], "description": "Env is a lightweight library bringing .env file parser compatibility to PHP. In short - it enables you to read .env files with PHP.", "homepage": "https://github.com/m1/Env", "keywords": [".env", "config", "dotenv", "env", "loader", "m1", "parser", "support"], "support": {"issues": "https://github.com/m1/Env/issues", "source": "https://github.com/m1/Env/tree/2.2.0"}, "install-path": "../m1/env"}, {"name": "marcj/topsort", "version": "2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/marcj/topsort.php.git", "reference": "972f58e42b5f110a0a1d8433247f65248abcfd5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marcj/topsort.php/zipball/972f58e42b5f110a0a1d8433247f65248abcfd5c", "reference": "972f58e42b5f110a0a1d8433247f65248abcfd5c", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"codeclimate/php-test-reporter": "dev-master", "phpunit/phpunit": "^9", "symfony/console": "~2.5 || ~3.0 || ~4.0"}, "time": "2020-09-24T12:39:55+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"MJS\\TopSort\\": "src/", "MJS\\TopSort\\Tests\\": "tests/Tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "High-Performance TopSort/Dependency resolving algorithm", "keywords": ["dependency resolving", "topological sort", "topsort"], "support": {"issues": "https://github.com/marcj/topsort.php/issues", "source": "https://github.com/marcj/topsort.php/tree/2.0.0"}, "funding": [{"url": "https://github.com/marcj", "type": "github"}], "install-path": "../marcj/topsort"}, {"name": "masterminds/html5", "version": "2.9.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "time": "2024-03-31T07:05:07+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "install-path": "../masterminds/html5"}, {"name": "ml/iri", "version": "1.1.4", "version_normalized": "*******", "target-dir": "ML/IRI", "source": {"type": "git", "url": "https://github.com/lanthaler/IRI.git", "reference": "cbd44fa913e00ea624241b38cefaa99da8d71341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lanthaler/IRI/zipball/cbd44fa913e00ea624241b38cefaa99da8d71341", "reference": "cbd44fa913e00ea624241b38cefaa99da8d71341", "shasum": ""}, "require": {"lib-pcre": ">=4.0", "php": ">=5.3.0"}, "time": "2014-01-21T13:43:39+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"ML\\IRI": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.markus-lanthaler.com", "role": "Developer"}], "description": "IRI handling for PHP", "homepage": "http://www.markus-lanthaler.com", "keywords": ["URN", "iri", "uri", "url"], "support": {"issues": "https://github.com/lanthaler/IRI/issues", "source": "https://github.com/lanthaler/IRI/tree/master"}, "install-path": "../ml/iri/ML/IRI"}, {"name": "ml/json-ld", "version": "1.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/lanthaler/JsonLD.git", "reference": "537e68e87a6bce23e57c575cd5dcac1f67ce25d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lanthaler/JsonLD/zipball/537e68e87a6bce23e57c575cd5dcac1f67ce25d8", "reference": "537e68e87a6bce23e57c575cd5dcac1f67ce25d8", "shasum": ""}, "require": {"ext-json": "*", "ml/iri": "^1.1.1", "php": ">=5.3.0"}, "require-dev": {"json-ld/tests": "1.0", "phpunit/phpunit": "^4"}, "time": "2022-09-29T08:45:17+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ML\\JsonLD\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.markus-lanthaler.com", "role": "Developer"}], "description": "JSON-LD Processor for PHP", "homepage": "http://www.markus-lanthaler.com", "keywords": ["JSON-LD", "j<PERSON>ld"], "support": {"issues": "https://github.com/lanthaler/JsonLD/issues", "source": "https://github.com/lanthaler/JsonLD/tree/1.2.1"}, "install-path": "../ml/json-ld"}, {"name": "monolog/monolog", "version": "3.9.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/10d85740180ecba7896c87e06a166e0c95a0e3b6", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "time": "2025-03-24T10:02:05+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.9.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "install-path": "../monolog/monolog"}, {"name": "nikic/php-parser", "version": "v4.19.4", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/715f4d25e225bc47b293a8b997fe6ce99bf987d2", "reference": "715f4d25e225bc47b293a8b997fe6ce99bf987d2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "time": "2024-09-29T15:01:53+00:00", "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.4"}, "install-path": "../nikic/php-parser"}, {"name": "oscarotero/html-parser", "version": "v0.1.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/oscarotero/html-parser.git", "reference": "10f3219267a365d9433f2f7d1694209c9d436c8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/oscarotero/html-parser/zipball/10f3219267a365d9433f2f7d1694209c9d436c8d", "reference": "10f3219267a365d9433f2f7d1694209c9d436c8d", "shasum": ""}, "require": {"php": "^7.2 || ^8"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.11", "phpunit/phpunit": "^8.0"}, "time": "2023-11-29T20:28:41+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"HtmlParser\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "Parse html strings to DOMDocument", "homepage": "https://github.com/oscarotero/html-parser", "keywords": ["dom", "html", "parser"], "support": {"email": "<EMAIL>", "issues": "https://github.com/oscarotero/html-parser/issues", "source": "https://github.com/oscarotero/html-parser/tree/v0.1.8"}, "install-path": "../oscarotero/html-parser"}, {"name": "psr/cache", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2021-02-03T23:26:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "install-path": "../psr/cache"}, {"name": "psr/container", "version": "2.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "time": "2021-11-05T16:47:00+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "install-path": "../psr/container"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "time": "2019-01-08T18:20:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "install-path": "../psr/event-dispatcher"}, {"name": "psr/http-client", "version": "1.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "time": "2023-09-23T14:17:50+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "install-path": "../psr/http-client"}, {"name": "psr/http-factory", "version": "1.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "time": "2024-04-15T12:06:14+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "install-path": "../psr/http-factory"}, {"name": "psr/http-message", "version": "1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:50:52+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "3.0.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2024-09-11T13:17:53+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "install-path": "../psr/log"}, {"name": "psr/simple-cache", "version": "3.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "time": "2021-10-29T13:26:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "install-path": "../psr/simple-cache"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "install-path": "../ralouphie/getallheaders"}, {"name": "sebastian/diff", "version": "4.0.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/ba01945089c3a293b01ba9badc29ad55b106b0bc", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "time": "2024-03-02T06:30:58+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "install-path": "../sebastian/diff"}, {"name": "silverstripe-themes/simple", "version": "3.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-simple.git", "reference": "ec62a3b15ad5dc8f5b37338325811cba63c1d564"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-simple/zipball/ec62a3b15ad5dc8f5b37338325811cba63c1d564", "reference": "ec62a3b15ad5dc8f5b37338325811cba63c1d564", "shasum": ""}, "require": {"composer/installers": "*", "silverstripe/framework": ">=3.5"}, "time": "2024-05-10T00:28:21+00:00", "type": "silverstripe-theme", "extra": {"expose": ["css", "images", "javascript", "webfonts"]}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> (Innovaif)", "homepage": "http://www.saratusar.com"}], "description": "The SilverStripe simple theme (default SilverStripe 3 theme)", "keywords": ["silverstripe", "theme"], "support": {"issues": "https://github.com/silverstripe/silverstripe-simple/issues", "source": "https://github.com/silverstripe/silverstripe-simple/tree/3.3.2"}, "install-path": "../../themes/simple"}, {"name": "silverstripe/admin", "version": "2.4.8", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-admin.git", "reference": "b68f641a3baccca8eb29ed31fbd092bbca7b014a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-admin/zipball/b68f641a3baccca8eb29ed31fbd092bbca7b014a", "reference": "b68f641a3baccca8eb29ed31fbd092bbca7b014a", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/framework": "^5.4", "silverstripe/vendor-plugin": "^2", "silverstripe/versioned": "^2"}, "conflict": {"dnadesign/silverstripe-elemental": "<5.3.0"}, "require-dev": {"phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/frameworktest": "^1", "silverstripe/standards": "^1", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-06-25T01:56:35+00:00", "type": "silverstripe-vendormodule", "extra": {"expose": ["client/dist", "client/lang", "thirdparty"]}, "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\Admin\\": "code/", "SilverStripe\\Admin\\Tests\\": "tests/php/", "SilverStripe\\Admin\\Tests\\Behat\\Context\\": "tests/behat/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "description": "SilverStripe admin interface", "homepage": "http://silverstripe.org", "keywords": ["admin", "silverstripe"], "support": {"issues": "https://github.com/silverstripe/silverstripe-admin/issues", "source": "https://github.com/silverstripe/silverstripe-admin/tree/2.4.8"}, "install-path": "../silverstripe/admin"}, {"name": "silverstripe/asset-admin", "version": "2.4.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-asset-admin.git", "reference": "271ccb8ce780c1e5dc9b20eea193fb6b62ab3608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-asset-admin/zipball/271ccb8ce780c1e5dc9b20eea193fb6b62ab3608", "reference": "271ccb8ce780c1e5dc9b20eea193fb6b62ab3608", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/admin": "^2.2", "silverstripe/framework": "^5.4", "silverstripe/graphql": "^5"}, "require-dev": {"phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/campaign-admin": "^2", "silverstripe/cms": "^5", "silverstripe/frameworktest": "^1", "silverstripe/standards": "^1", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-05-25T22:02:07+00:00", "type": "silverstripe-vendormodule", "extra": {"expose": ["client/dist", "client/lang"]}, "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\AssetAdmin\\": "code/", "SilverStripe\\AssetAdmin\\Tests\\": "tests/php/", "SilverStripe\\AssetAdmin\\Tests\\Behat\\Context\\": "tests/behat/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Asset management for the SilverStripe CMS", "support": {"issues": "https://github.com/silverstripe/silverstripe-asset-admin/issues", "source": "https://github.com/silverstripe/silverstripe-asset-admin/tree/2.4.3"}, "install-path": "../silverstripe/asset-admin"}, {"name": "silverstripe/assets", "version": "2.4.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-assets.git", "reference": "815fe1c78bd1d470713651c65fa8968845f3cc21"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-assets/zipball/815fe1c78bd1d470713651c65fa8968845f3cc21", "reference": "815fe1c78bd1d470713651c65fa8968845f3cc21", "shasum": ""}, "require": {"intervention/image": "^2.7.2", "league/flysystem": "^3.9.0", "php": "^8.1", "silverstripe/framework": "^5.1", "silverstripe/vendor-plugin": "^2", "symfony/filesystem": "^6.1"}, "require-dev": {"mikey179/vfsstream": "^v1.6.11", "phpstan/extension-installer": "^1.3", "silverstripe/recipe-testing": "^3", "silverstripe/standards": "^1", "silverstripe/versioned": "^2", "squizlabs/php_codesniffer": "^3.7"}, "suggest": {"ext-exif": "If you use GD backend (the default) you may want to have EXIF extension installed to elude some tricky issues"}, "time": "2025-05-13T06:43:54+00:00", "type": "silverstripe-vendormodule", "extra": {"installer-name": "silverstripe-assets"}, "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\Assets\\": "src/", "SilverStripe\\Assets\\Tests\\": "tests/php/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "description": "SilverStripe Assets component", "homepage": "http://silverstripe.org", "keywords": ["assets", "silverstripe"], "support": {"issues": "https://github.com/silverstripe/silverstripe-assets/issues", "source": "https://github.com/silverstripe/silverstripe-assets/tree/2.4.2"}, "install-path": "../silverstripe/assets"}, {"name": "silverstripe/campaign-admin", "version": "2.4.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-campaign-admin.git", "reference": "905555d30fa49a68f6161b25d9f37f4594d9fab9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-campaign-admin/zipball/905555d30fa49a68f6161b25d9f37f4594d9fab9", "reference": "905555d30fa49a68f6161b25d9f37f4594d9fab9", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/admin": "^2", "silverstripe/framework": "^5.4", "silverstripe/vendor-plugin": "^2", "silverstripe/versioned": "^2"}, "require-dev": {"phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/asset-admin": "^2", "silverstripe/cms": "^5", "silverstripe/standards": "^1", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-05-19T02:09:12+00:00", "type": "silverstripe-vendormodule", "extra": {"expose": ["client/dist", "client/lang"]}, "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\CampaignAdmin\\": "src/", "SilverStripe\\CampaignAdmin\\Tests\\": "tests/php/", "SilverStripe\\CampaignAdmin\\Tests\\Behat\\Context\\": "tests/behat/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "description": "SilverStripe campaign admin interface", "homepage": "http://silverstripe.org", "keywords": ["admin", "campaign", "silverstripe", "versioned"], "support": {"issues": "https://github.com/silverstripe/silverstripe-campaign-admin/issues", "source": "https://github.com/silverstripe/silverstripe-campaign-admin/tree/2.4.2"}, "install-path": "../silverstripe/campaign-admin"}, {"name": "silverstripe/cms", "version": "5.4.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-cms.git", "reference": "9af9b9186a6dd45d4cc6d4c26f83ba8bba782b66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-cms/zipball/9af9b9186a6dd45d4cc6d4c26f83ba8bba782b66", "reference": "9af9b9186a6dd45d4cc6d4c26f83ba8bba782b66", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/admin": "^2.2", "silverstripe/campaign-admin": "^2", "silverstripe/framework": "^5.4", "silverstripe/reports": "^5", "silverstripe/siteconfig": "^5", "silverstripe/vendor-plugin": "^2", "silverstripe/versioned": "^2", "silverstripe/versioned-admin": "^2"}, "require-dev": {"phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/standards": "^1", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-05-26T22:51:55+00:00", "type": "silverstripe-vendormodule", "extra": {"expose": ["client/dist", "client/lang"]}, "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\CMS\\": ["code/", "_legacy/"], "SilverStripe\\CMS\\Tests\\": "code/php/", "SilverStripe\\CMS\\Tests\\Behaviour\\": "tests/behat/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "description": "The SilverStripe Content Management System", "homepage": "http://silverstripe.org", "keywords": ["cms", "silverstripe"], "support": {"issues": "https://github.com/silverstripe/silverstripe-cms/issues", "source": "https://github.com/silverstripe/silverstripe-cms/tree/5.4.3"}, "install-path": "../silverstripe/cms"}, {"name": "silverstripe/config", "version": "2.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-config.git", "reference": "1cbb24bea999342717aa7577752cbf04065b68ec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-config/zipball/1cbb24bea999342717aa7577752cbf04065b68ec", "reference": "1cbb24bea999342717aa7577752cbf04065b68ec", "shasum": ""}, "require": {"marcj/topsort": "^2.0.0", "php": "^8.1", "psr/simple-cache": "^3.0.0", "symfony/finder": "^6.1", "symfony/yaml": "^6.1"}, "require-dev": {"mikey179/vfsstream": "^1.6", "phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/standards": "^1", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-05-15T23:24:12+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\Config\\": "src/", "SilverStripe\\Config\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SilverStripe configuration based on YAML and class statics", "support": {"issues": "https://github.com/silverstripe/silverstripe-config/issues", "source": "https://github.com/silverstripe/silverstripe-config/tree/2.2.1"}, "install-path": "../silverstripe/config"}, {"name": "silverstripe/errorpage", "version": "2.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-errorpage.git", "reference": "489f7d31fb53ed71e363a13b3d3ecc420fa8b78c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-errorpage/zipball/489f7d31fb53ed71e363a13b3d3ecc420fa8b78c", "reference": "489f7d31fb53ed71e363a13b3d3ecc420fa8b78c", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/cms": "^5", "silverstripe/framework": "^5", "silverstripe/vendor-plugin": "^2"}, "require-dev": {"phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/standards": "^1", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-04-08T03:16:50+00:00", "type": "silverstripe-vendormodule", "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\ErrorPage\\": "src/", "SilverStripe\\ErrorPage\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "description": "ErrorPage component for SilverStripe CMS", "homepage": "http://silverstripe.org", "keywords": ["error", "errorpage", "silverstripe"], "support": {"issues": "https://github.com/silverstripe/silverstripe-errorpage/issues", "source": "https://github.com/silverstripe/silverstripe-errorpage/tree/2.4.0"}, "install-path": "../silverstripe/errorpage"}, {"name": "silverstripe/event-dispatcher", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-event-dispatcher.git", "reference": "aea42ed97039f7262217eab4d8325579ac4be18b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-event-dispatcher/zipball/aea42ed97039f7262217eab4d8325579ac4be18b", "reference": "aea42ed97039f7262217eab4d8325579ac4be18b", "shasum": ""}, "require": {"php": "^8.1", "psr/event-dispatcher": "^1", "silverstripe/framework": "^5", "symfony/event-dispatcher": "^6.1"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "^3.7"}, "time": "2024-06-17T00:39:41+00:00", "type": "silverstripe-vendormodule", "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\EventDispatcher\\": "src/", "SilverStripe\\EventDispatcher\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Publish and subscribe to events in Silverstripe CMS or your Silverstripe application", "keywords": ["cms", "events", "silverstripe"], "support": {"issues": "https://github.com/silverstripe/silverstripe-event-dispatcher/issues", "source": "https://github.com/silverstripe/silverstripe-event-dispatcher/tree/1.0.1"}, "install-path": "../silverstripe/event-dispatcher"}, {"name": "silverstripe/framework", "version": "5.4.12", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-framework.git", "reference": "831ea2bb23b4650c70105cb5ef5118a1c0dbd509"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-framework/zipball/831ea2bb23b4650c70105cb5ef5118a1c0dbd509", "reference": "831ea2bb23b4650c70105cb5ef5118a1c0dbd509", "shasum": ""}, "require": {"composer-runtime-api": "^2.0", "composer/installers": "^2.2", "embed/embed": "^4.4.7", "ext-ctype": "*", "ext-dom": "*", "ext-hash": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-session": "*", "ext-simplexml": "*", "ext-tokenizer": "*", "ext-xml": "*", "guzzlehttp/guzzle": "^7.5.0", "guzzlehttp/psr7": "^2.4.0", "league/csv": "^9.8.0", "m1/env": "^2.2.0", "masterminds/html5": "^2.7.6", "monolog/monolog": "^3.2.0", "nikic/php-parser": "^4.15.0", "php": "^8.1", "psr/container": "^1.1 || ^2.0", "psr/http-message": "^1", "sebastian/diff": "^4.0", "silverstripe/assets": "^2.3", "silverstripe/config": "^2.2", "silverstripe/supported-modules": "^1.1", "silverstripe/vendor-plugin": "^2", "sminnee/callbacklist": "^0.1.1", "symfony/cache": "^6.1", "symfony/config": "^6.1", "symfony/dom-crawler": "^6.1", "symfony/filesystem": "^6.1", "symfony/http-foundation": "^6.1", "symfony/mailer": "^6.1", "symfony/mime": "^6.1", "symfony/translation": "^6.1", "symfony/validator": "^6.1", "symfony/yaml": "^6.1"}, "conflict": {"egulias/email-validator": "^2", "oscarotero/html-parser": "<0.1.7", "silverstripe/behat-extension": "<5.5", "silverstripe/mfa": "<5.4", "symfony/process": "<5.3.7"}, "provide": {"psr/container-implementation": "1.0.0"}, "require-dev": {"composer/semver": "^3.4", "phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/standards": "^1", "silverstripe/versioned": "^2", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-07-03T21:45:43+00:00", "bin": ["sake"], "type": "silverstripe-vendormodule", "extra": {"expose": ["client/images", "client/styles"]}, "installation-source": "dist", "autoload": {"files": ["src/includes/constants.php"], "psr-4": {"SilverStripe\\Dev\\": "src/Dev/", "SilverStripe\\ORM\\": "src/ORM/", "SilverStripe\\Core\\": "src/Core/", "SilverStripe\\View\\": "src/View/", "SilverStripe\\i18n\\": "src/i18n/", "SilverStripe\\Forms\\": "src/Forms/", "SilverStripe\\Control\\": "src/Control/", "SilverStripe\\Logging\\": "src/Logging/", "SilverStripe\\Security\\": "src/Security/", "SilverStripe\\Dev\\Tests\\": "tests/php/Dev/", "SilverStripe\\ORM\\Tests\\": "tests/php/ORM/", "SilverStripe\\Core\\Tests\\": "tests/php/Core/", "SilverStripe\\View\\Tests\\": "tests/php/View/", "SilverStripe\\i18n\\Tests\\": "tests/php/i18n/", "SilverStripe\\Forms\\Tests\\": "tests/php/Forms/", "SilverStripe\\Control\\Tests\\": "tests/php/Control/", "SilverStripe\\Logging\\Tests\\": "tests/php/Logging/", "SilverStripe\\Security\\Tests\\": "tests/php/Security/", "SilverStripe\\Framework\\Tests\\Behaviour\\": "tests/behat/src/"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["thirdparty/"], "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "description": "The SilverStripe framework", "homepage": "http://silverstripe.org", "keywords": ["framework", "silverstripe"], "support": {"issues": "https://github.com/silverstripe/silverstripe-framework/issues", "source": "https://github.com/silverstripe/silverstripe-framework/tree/5.4.12"}, "install-path": "../silverstripe/framework"}, {"name": "silverstripe/graphql", "version": "5.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-graphql.git", "reference": "3a240b343af5294228c49dcacf669ea0f50329f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-graphql/zipball/3a240b343af5294228c49dcacf669ea0f50329f1", "reference": "3a240b343af5294228c49dcacf669ea0f50329f1", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.5.0", "guzzlehttp/psr7": "^2.4.1", "m1/env": "^2.2.0", "php": "^8.1", "silverstripe/event-dispatcher": "^1", "silverstripe/framework": "^5.4", "silverstripe/vendor-plugin": "^2", "webonyx/graphql-php": "^15.0.1"}, "require-dev": {"dnadesign/silverstripe-elemental": "^5", "phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/asset-admin": "^2", "silverstripe/frameworktest": "^1", "silverstripe/standards": "^1", "silverstripe/versioned-admin": "^2", "squizlabs/php_codesniffer": "^3.7"}, "suggest": {"silverstripe/assets": "Allows schema to be cached in the filesystem.", "silverstripe/graphql-devtools": "Adds in-browser tools for testing and debugging GraphQL queries"}, "time": "2025-04-30T23:26:03+00:00", "type": "silverstripe-vendormodule", "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\GraphQL\\": "src/", "SilverStripe\\GraphQL\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "GraphQL server for SilverStripe models and other data", "support": {"issues": "https://github.com/silverstripe/silverstripe-graphql/issues", "source": "https://github.com/silverstripe/silverstripe-graphql/tree/5.3.1"}, "install-path": "../silverstripe/graphql"}, {"name": "silverstripe/login-forms", "version": "5.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-login-forms.git", "reference": "752394c33ba21724e74d4e2e124926b908f5c679"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-login-forms/zipball/752394c33ba21724e74d4e2e124926b908f5c679", "reference": "752394c33ba21724e74d4e2e124926b908f5c679", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/framework": "^5"}, "require-dev": {"phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/standards": "^1", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-05-26T22:34:52+00:00", "type": "silverstripe-vendormodule", "extra": {"expose": ["client/dist"]}, "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\LoginForms\\": "src/", "SilverStripe\\LoginForms\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "A collection of templates for the CMS login screens", "homepage": "https://github.com/silverstripe/login-forms", "keywords": ["login", "silverstripe", "styling", "template"], "support": {"issues": "https://github.com/silverstripe/login-forms/issues", "source": "https://github.com/silverstripe/silverstripe-login-forms/tree/5.3.1"}, "install-path": "../silverstripe/login-forms"}, {"name": "silverstripe/mimevalidator", "version": "3.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-mimevalidator.git", "reference": "f21448ebc831201867ca6d10ebc1ccc838864a97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-mimevalidator/zipball/f21448ebc831201867ca6d10ebc1ccc838864a97", "reference": "f21448ebc831201867ca6d10ebc1ccc838864a97", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^8.1", "silverstripe/framework": "^5"}, "require-dev": {"monolog/monolog": "^3.2.0", "nikic/php-parser": "^4.15.0", "phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/standards": "^1", "squizlabs/php_codesniffer": "^3.7"}, "time": "2024-02-12T12:18:01+00:00", "type": "silverstripe-vendormodule", "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\MimeValidator\\": "src/", "SilverStripe\\MimeValidator\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Checks uploaded file content roughly matches a known MIME type for the file extension.", "keywords": ["cwp", "fileinfo", "mime", "silverstripe", "upload", "validator"], "support": {"issues": "https://github.com/silverstripe/silverstripe-mimevalidator/issues", "source": "https://github.com/silverstripe/silverstripe-mimevalidator/tree/3.1.0"}, "install-path": "../silverstripe/mimevalidator"}, {"name": "silverstripe/recipe-cms", "version": "5.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/recipe-cms.git", "reference": "0db42df22bbe021bd8cd1999e80fc92901bd7424"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/recipe-cms/zipball/0db42df22bbe021bd8cd1999e80fc92901bd7424", "reference": "0db42df22bbe021bd8cd1999e80fc92901bd7424", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/admin": "~2.4.0@stable", "silverstripe/asset-admin": "~2.4.0@stable", "silverstripe/campaign-admin": "~2.4.0@stable", "silverstripe/cms": "~5.4.0@stable", "silverstripe/errorpage": "~2.4.0@stable", "silverstripe/graphql": "~5.3.0@stable", "silverstripe/recipe-core": "~5.4.0@stable", "silverstripe/recipe-plugin": "~2.1.0@stable", "silverstripe/reports": "~5.4.0@stable", "silverstripe/session-manager": "~2.3.3@stable", "silverstripe/siteconfig": "~5.4.0@stable", "silverstripe/versioned": "~2.4.0@stable", "silverstripe/versioned-admin": "~2.4.0@stable"}, "require-dev": {"phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/standards": "^1"}, "time": "2025-04-09T23:49:28+00:00", "type": "silverstripe-recipe", "extra": {"project-files": ["app/src/*"]}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SilverStripe recipe for fully featured page and asset content editing", "homepage": "http://silverstripe.org", "support": {"issues": "https://github.com/silverstripe/recipe-cms/issues", "source": "https://github.com/silverstripe/recipe-cms/tree/5.4.0"}, "install-path": "../silverstripe/recipe-cms"}, {"name": "silverstripe/recipe-core", "version": "5.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/recipe-core.git", "reference": "2e6af93b5a29d14bf7208e4fbabef2a1ee81f4b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/recipe-core/zipball/2e6af93b5a29d14bf7208e4fbabef2a1ee81f4b9", "reference": "2e6af93b5a29d14bf7208e4fbabef2a1ee81f4b9", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/assets": "~2.4.0@stable", "silverstripe/config": "~2.2.0@stable", "silverstripe/framework": "~5.4.0@stable", "silverstripe/mimevalidator": "~3.1.0@stable", "silverstripe/recipe-plugin": "~2.1.0@stable"}, "require-dev": {"mikey179/vfsstream": "^1.6.11", "phpunit/phpunit": "^9.6", "silverstripe/versioned": "^2"}, "time": "2025-04-09T23:47:14+00:00", "type": "silverstripe-recipe", "extra": {"public-files": [".htaccess", "web.config", "index.php"], "project-files": [".htaccess", "app/*"]}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "SilverStripe framework-only core recipe", "homepage": "http://silverstripe.org", "support": {"issues": "https://github.com/silverstripe/recipe-core/issues", "source": "https://github.com/silverstripe/recipe-core/tree/5.4.0"}, "install-path": "../silverstripe/recipe-core"}, {"name": "silverstripe/recipe-plugin", "version": "2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/recipe-plugin.git", "reference": "57d785dba26370ad9b981871411930ccca1dbffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/recipe-plugin/zipball/57d785dba26370ad9b981871411930ccca1dbffd", "reference": "57d785dba26370ad9b981871411930ccca1dbffd", "shasum": ""}, "require": {"composer-plugin-api": "^2", "php": "^8.1"}, "require-dev": {"composer/composer": "^2", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-02-19T23:31:22+00:00", "type": "composer-plugin", "extra": {"class": "SilverStripe\\RecipePlugin\\RecipePlugin"}, "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\RecipePlugin\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Helper plugin to install SilverStripe recipes", "support": {"issues": "https://github.com/silverstripe/recipe-plugin/issues", "source": "https://github.com/silverstripe/recipe-plugin/tree/2.1.0"}, "install-path": "../silverstripe/recipe-plugin"}, {"name": "silverstripe/reports", "version": "5.4.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-reports.git", "reference": "04dd08096ea5030e34784c8bd85e09516d47db40"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-reports/zipball/04dd08096ea5030e34784c8bd85e09516d47db40", "reference": "04dd08096ea5030e34784c8bd85e09516d47db40", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/admin": "^2", "silverstripe/assets": "^2", "silverstripe/config": "^2", "silverstripe/framework": "^5.2", "silverstripe/vendor-plugin": "^2", "silverstripe/versioned": "^2"}, "require-dev": {"phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/standards": "^1", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-05-19T02:09:19+00:00", "type": "silverstripe-vendormodule", "extra": {"expose": ["javascript"]}, "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\Reports\\": "code/", "SilverStripe\\Reports\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "description": "Reports module for SilverStripe CMS", "homepage": "http://silverstripe.org", "keywords": ["cms", "reports", "silverstripe"], "support": {"issues": "https://github.com/silverstripe/silverstripe-reports/issues", "source": "https://github.com/silverstripe/silverstripe-reports/tree/5.4.1"}, "install-path": "../silverstripe/reports"}, {"name": "silverstripe/session-manager", "version": "2.3.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-session-manager.git", "reference": "cbb3f34d8ecfb89dbe82da43d89d685ca219cb02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-session-manager/zipball/cbb3f34d8ecfb89dbe82da43d89d685ca219cb02", "reference": "cbb3f34d8ecfb89dbe82da43d89d685ca219cb02", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/admin": "^2", "silverstripe/framework": "^5.3", "symfony/http-foundation": "^6.1", "ua-parser/uap-php": "^3.9.14"}, "require-dev": {"phpstan/extension-installer": "^1.3", "silverstripe/recipe-testing": "^3", "silverstripe/standards": "^1", "symbiote/silverstripe-queuedjobs": "^5"}, "suggest": {"silverstripe/auditor": "^3", "symbiote/silverstripe-queuedjobs": "^5"}, "time": "2025-04-03T22:25:52+00:00", "type": "silverstripe-vendormodule", "extra": {"expose": ["client/dist"]}, "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\SessionManager\\": "src/", "SilverStripe\\SessionManager\\Tests\\": "tests/php/", "SilverStripe\\SessionManager\\Tests\\Behat\\Context\\": "tests/behat/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>z <PERSON>", "homepage": "https://github.com/kinglozzer"}, {"name": "Silverstripe Ltd.", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "description": "Allow users to manage and revoke access to multiple login sessions across devices.", "homepage": "https://github.com/silverstripe/silverstripe-session-manager", "keywords": ["session", "silverstripe"], "support": {"issues": "https://github.com/silverstripe/silverstripe-session-manager/issues", "source": "https://github.com/silverstripe/silverstripe-session-manager/tree/2.3.3"}, "install-path": "../silverstripe/session-manager"}, {"name": "silverstripe/siteconfig", "version": "5.4.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-siteconfig.git", "reference": "c08b86cbb3372c47edc7f52ca03a047452fce917"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-siteconfig/zipball/c08b86cbb3372c47edc7f52ca03a047452fce917", "reference": "c08b86cbb3372c47edc7f52ca03a047452fce917", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/admin": "^2.2", "silverstripe/framework": "^5.2", "silverstripe/vendor-plugin": "^2"}, "require-dev": {"phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/standards": "^1", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-04-30T23:25:52+00:00", "type": "silverstripe-vendormodule", "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\SiteConfig\\": "code/", "SilverStripe\\SiteConfig\\Tests\\": "tests/php/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Site wide settings administration.", "keywords": ["silverstripe", "siteconfig"], "support": {"issues": "https://github.com/silverstripe/silverstripe-siteconfig/issues", "source": "https://github.com/silverstripe/silverstripe-siteconfig/tree/5.4.1"}, "install-path": "../silverstripe/siteconfig"}, {"name": "silverstripe/supported-modules", "version": "1.1.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/supported-modules.git", "reference": "d9975ae0ed1b92ca157d29e0a9d91fefcf7c40cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/supported-modules/zipball/d9975ae0ed1b92ca157d29e0a9d91fefcf7c40cd", "reference": "d9975ae0ed1b92ca157d29e0a9d91fefcf7c40cd", "shasum": ""}, "require": {"composer/semver": "^3.4", "php": "^8.1"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "time": "2025-06-10T04:59:27+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\SupportedModules\\": "src/", "SilverStripe\\SupportedModules\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Metadata about Silverstripe CMS supported modules and other repositories maintained by Silverstripe", "support": {"issues": "https://github.com/silverstripe/supported-modules/issues", "source": "https://github.com/silverstripe/supported-modules/tree/1.1.5"}, "install-path": "../silverstripe/supported-modules"}, {"name": "silverstripe/vendor-plugin", "version": "2.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/vendor-plugin.git", "reference": "b9c18fa8488188908935c6a71222b637f3cfe64b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/vendor-plugin/zipball/b9c18fa8488188908935c6a71222b637f3cfe64b", "reference": "b9c18fa8488188908935c6a71222b637f3cfe64b", "shasum": ""}, "require": {"composer-plugin-api": "^2", "composer/installers": "^2", "php": "^8.1"}, "require-dev": {"composer/composer": "^2", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-04-08T03:16:22+00:00", "type": "composer-plugin", "extra": {"class": "SilverStripe\\VendorPlugin\\VendorPlugin"}, "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\VendorPlugin\\": "src/", "SilverStripe\\VendorPlugin\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows vendor modules to expose directories to the webroot", "support": {"issues": "https://github.com/silverstripe/vendor-plugin/issues", "source": "https://github.com/silverstripe/vendor-plugin/tree/2.1.0"}, "install-path": "../silverstripe/vendor-plugin"}, {"name": "silverstripe/versioned", "version": "2.4.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-versioned.git", "reference": "fb3085391c6596d2ed9aed00683965ddb349087a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-versioned/zipball/fb3085391c6596d2ed9aed00683965ddb349087a", "reference": "fb3085391c6596d2ed9aed00683965ddb349087a", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/framework": "^5.4", "silverstripe/vendor-plugin": "^2", "symfony/cache": "^6.1"}, "require-dev": {"phpstan/extension-installer": "^1.3", "silverstripe/graphql": "^5", "silverstripe/recipe-testing": "^3", "silverstripe/standards": "^1", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-05-19T02:09:25+00:00", "type": "silverstripe-vendormodule", "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\Versioned\\": "src/", "SilverStripe\\Versioned\\Tests\\": "tests/php/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "description": "SilverStripe Versioned component", "homepage": "http://silverstripe.org", "keywords": ["silverstripe", "versioned"], "support": {"issues": "https://github.com/silverstripe/silverstripe-versioned/issues", "source": "https://github.com/silverstripe/silverstripe-versioned/tree/2.4.3"}, "install-path": "../silverstripe/versioned"}, {"name": "silverstripe/versioned-admin", "version": "2.4.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/silverstripe/silverstripe-versioned-admin.git", "reference": "e242940eabff298e2829a07366c1982966e80d67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silverstripe/silverstripe-versioned-admin/zipball/e242940eabff298e2829a07366c1982966e80d67", "reference": "e242940eabff298e2829a07366c1982966e80d67", "shasum": ""}, "require": {"php": "^8.1", "silverstripe/admin": "^2", "silverstripe/framework": "^5", "silverstripe/graphql": "^5", "silverstripe/vendor-plugin": "^2", "silverstripe/versioned": "^2"}, "require-dev": {"phpstan/extension-installer": "^1.3", "phpunit/phpunit": "^9.6", "silverstripe/cms": "^5", "silverstripe/frameworktest": "^1", "silverstripe/standards": "^1", "squizlabs/php_codesniffer": "^3.7"}, "time": "2025-06-16T02:20:46+00:00", "type": "silverstripe-vendormodule", "extra": {"expose": ["client/dist"]}, "installation-source": "dist", "autoload": {"psr-4": {"SilverStripe\\VersionedAdmin\\": "src/", "SilverStripe\\VersionedAdmin\\Tests\\": "tests/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "description": "SilverStripe versioned admin interface", "homepage": "http://silverstripe.org", "keywords": ["admin", "silverstripe", "versioned"], "support": {"issues": "https://github.com/silverstripe/silverstripe-versioned-admin/issues", "source": "https://github.com/silverstripe/silverstripe-versioned-admin/tree/2.4.3"}, "install-path": "../silverstripe/versioned-admin"}, {"name": "sminnee/callbacklist", "version": "0.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sminnee/callbacklist.git", "reference": "8c9f0a3a9f57aaa8cadb72eb579f5550a73abbc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sminnee/callbacklist/zipball/8c9f0a3a9f57aaa8cadb72eb579f5550a73abbc4", "reference": "8c9f0a3a9f57aaa8cadb72eb579f5550a73abbc4", "shasum": ""}, "require": {"php": "^7.1 || ^8"}, "require-dev": {"php-parallel-lint/php-console-highlighter": "^0.5.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-strict-rules": "^0.12.5", "phpunit/phpunit": "^7 || ^8 || ^9", "slevomat/coding-standard": "^6.4", "squizlabs/php_codesniffer": "^3.5"}, "time": "2020-12-06T22:00:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Sminnee\\CallbackList\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP class that manages a list of callbacks", "support": {"issues": "https://github.com/sminnee/callbacklist/issues", "source": "https://github.com/sminnee/callbacklist/tree/0.1.1"}, "install-path": "../sminnee/callbacklist"}, {"name": "symfony/cache", "version": "v6.4.23", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "c88690befb8d4a85dc321fb78d677507f5eb141b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/c88690befb8d4a85dc321fb78d677507f5eb141b", "reference": "c88690befb8d4a85dc321fb78d677507f5eb141b", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.3.6|^7.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/var-dumper": "<5.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "time": "2025-06-27T18:31:36+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/cache"}, {"name": "symfony/cache-contracts", "version": "v3.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/5d68a57d66910405e5c0b63d6f0af941e66fc868", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "time": "2025-03-13T15:25:07+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/cache-contracts"}, {"name": "symfony/config", "version": "v6.4.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "af5917a3b1571f54689e56677a3f06440d2fe4c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/af5917a3b1571f54689e56677a3f06440d2fe4c7", "reference": "af5917a3b1571f54689e56677a3f06440d2fe4c7", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^5.4|^6.0|^7.0", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<5.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "time": "2025-05-14T06:00:01+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/config"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-25T14:21:43+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "installation-source": "dist", "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/deprecation-contracts"}, {"name": "symfony/dom-crawler", "version": "v6.4.23", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "22210aacb35dbadd772325d759d17bce2374a84d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/22210aacb35dbadd772325d759d17bce2374a84d", "reference": "22210aacb35dbadd772325d759d17bce2374a84d", "shasum": ""}, "require": {"masterminds/html5": "^2.6", "php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "^5.4|^6.0|^7.0"}, "time": "2025-06-13T12:10:00+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/dom-crawler"}, {"name": "symfony/event-dispatcher", "version": "v6.4.13", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "reference": "0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e", "shasum": ""}, "require": {"php": ">=8.1", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/error-handler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^5.4|^6.0|^7.0"}, "time": "2024-09-25T14:18:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/event-dispatcher"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "59eb412e93815df44f05f342958efa9f46b1e586"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/59eb412e93815df44f05f342958efa9f46b1e586", "reference": "59eb412e93815df44f05f342958efa9f46b1e586", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "time": "2024-09-25T14:21:43+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/event-dispatcher-contracts"}, {"name": "symfony/filesystem", "version": "v6.4.13", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/4856c9cf585d5a0313d8d35afd681a526f038dd3", "reference": "4856c9cf585d5a0313d8d35afd681a526f038dd3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^5.4|^6.4|^7.0"}, "time": "2024-10-25T15:07:50+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/filesystem"}, {"name": "symfony/finder", "version": "v6.4.17", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "time": "2024-12-29T13:51:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.17"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/finder"}, {"name": "symfony/http-foundation", "version": "v6.4.23", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "452d19f945ee41345fd8a50c18b60783546b7bd3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/452d19f945ee41345fd8a50c18b60783546b7bd3", "reference": "452d19f945ee41345fd8a50c18b60783546b7bd3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "time": "2025-05-26T09:17:58+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/http-foundation"}, {"name": "symfony/mailer", "version": "v6.4.23", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "a480322ddf8e54de262c9bca31fdcbe26b553de5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/a480322ddf8e54de262c9bca31fdcbe26b553de5", "reference": "a480322ddf8e54de262c9bca31fdcbe26b553de5", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.1", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/mime": "^6.2|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/messenger": "<6.2", "symfony/mime": "<6.2", "symfony/twig-bridge": "<6.2.1"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/messenger": "^6.2|^7.0", "symfony/twig-bridge": "^6.2|^7.0"}, "time": "2025-06-26T21:24:02+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/mailer"}, {"name": "symfony/mime", "version": "v6.4.21", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "fec8aa5231f3904754955fad33c2db50594d22d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/fec8aa5231f3904754955fad33c2db50594d22d1", "reference": "fec8aa5231f3904754955fad33c2db50594d22d1", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.4|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "time": "2025-04-27T13:27:38+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/mime"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-10T14:38:51+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-idn"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-intl-normalizer"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2024-12-23T08:48:59+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "symfony/polyfill-php83", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "time": "2024-09-09T11:45:10+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-php83"}, {"name": "symfony/service-contracts", "version": "v3.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "time": "2025-04-25T09:37:31+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/service-contracts"}, {"name": "symfony/translation", "version": "v6.4.23", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "de8afa521e04a5220e9e58a1dc99971ab7cac643"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/de8afa521e04a5220e9e58a1dc99971ab7cac643", "reference": "de8afa521e04a5220e9e58a1dc99971ab7cac643", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "time": "2025-06-26T21:24:02+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation"}, {"name": "symfony/translation-contracts", "version": "v3.6.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "shasum": ""}, "require": {"php": ">=8.1"}, "time": "2024-09-27T08:32:26+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/translation-contracts"}, {"name": "symfony/validator", "version": "v6.4.23", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "6506760ab57e7cda5bde9cdaed736526162284bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/6506760ab57e7cda5bde9cdaed736526162284bc", "reference": "6506760ab57e7cda5bde9cdaed736526162284bc", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php83": "^1.27", "symfony/translation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.13", "doctrine/lexer": "<1.1", "symfony/dependency-injection": "<5.4", "symfony/expression-language": "<5.4", "symfony/http-kernel": "<5.4", "symfony/intl": "<5.4", "symfony/property-info": "<5.4", "symfony/translation": "<5.4.35|>=6.0,<6.3.12|>=6.4,<6.4.3|>=7.0,<7.0.3", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.13|^2", "egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4.35|~6.3.12|^6.4.3|^7.0.3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "time": "2025-06-26T07:25:45+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/validator"}, {"name": "symfony/var-exporter", "version": "v6.4.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "f28cf841f5654955c9f88ceaf4b9dc29571988a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/f28cf841f5654955c9f88ceaf4b9dc29571988a9", "reference": "f28cf841f5654955c9f88ceaf4b9dc29571988a9", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "time": "2025-05-14T13:00:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/var-exporter"}, {"name": "symfony/yaml", "version": "v6.4.23", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "93e29e0deb5f1b2e360adfb389a20d25eb81a27b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/93e29e0deb5f1b2e360adfb389a20d25eb81a27b", "reference": "93e29e0deb5f1b2e360adfb389a20d25eb81a27b", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "time": "2025-06-03T06:46:12+00:00", "bin": ["Resources/bin/yaml-lint"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v6.4.23"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/yaml"}, {"name": "ua-parser/uap-php", "version": "v3.9.14", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/ua-parser/uap-php.git", "reference": "b796c5ea5df588e65aeb4e2c6cce3811dec4fed6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ua-parser/uap-php/zipball/b796c5ea5df588e65aeb4e2c6cce3811dec4fed6", "reference": "b796c5ea5df588e65aeb4e2c6cce3811dec4fed6", "shasum": ""}, "require": {"composer/ca-bundle": "^1.1", "php": "^7.2 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.33", "phpunit/phpunit": "^8 || ^9", "symfony/console": "^3.4 || ^4.2 || ^4.3 || ^5.0", "symfony/filesystem": "^3.4 || ^4.2 ||  ^4.3 || ^5.0", "symfony/finder": "^3.4 || ^4.2 || ^4.3 || ^5.0", "symfony/yaml": "^3.4 || ^4.2 || ^4.3 || ^5.0", "vimeo/psalm": "^3.12"}, "suggest": {"symfony/console": "Required for CLI usage - ^3.4 || ^4.3 || ^5.0", "symfony/filesystem": "Required for CLI usage - ^3.4 || ^4.3 || ^5.0", "symfony/finder": "Required for CLI usage - ^3.4 || ^4.3 || ^5.0", "symfony/yaml": "Required for CLI usage - ^3.4 || ^4.3 || ^5.0"}, "time": "2020-10-02T23:36:20+00:00", "bin": ["bin/uaparser"], "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"UAParser\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A multi-language port of Browserscope's user agent parser.", "support": {"issues": "https://github.com/ua-parser/uap-php/issues", "source": "https://github.com/ua-parser/uap-php/tree/v3.9.14"}, "install-path": "../ua-parser/uap-php"}, {"name": "webonyx/graphql-php", "version": "v15.21.2", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/webonyx/graphql-php.git", "reference": "1a7aac3b7f8d66fd52aa7d1b7eafad3dec1f9cb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webonyx/graphql-php/zipball/1a7aac3b7f8d66fd52aa7d1b7eafad3dec1f9cb5", "reference": "1a7aac3b7f8d66fd52aa7d1b7eafad3dec1f9cb5", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^7.4 || ^8"}, "require-dev": {"amphp/amp": "^2.6", "amphp/http-server": "^2.1", "dms/phpunit-arraysubset-asserts": "dev-master", "ergebnis/composer-normalize": "^2.28", "friendsofphp/php-cs-fixer": "3.82.2", "mll-lab/php-cs-fixer-config": "5.11.0", "nyholm/psr7": "^1.5", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "2.1.17", "phpstan/phpstan-phpunit": "2.0.6", "phpstan/phpstan-strict-rules": "2.0.4", "phpunit/phpunit": "^9.5 || ^10.5.21 || ^11", "psr/http-message": "^1 || ^2", "react/http": "^1.6", "react/promise": "^2.0 || ^3.0", "rector/rector": "^2.0", "symfony/polyfill-php81": "^1.23", "symfony/var-exporter": "^5 || ^6 || ^7", "thecodingmachine/safe": "^1.3 || ^2 || ^3"}, "suggest": {"amphp/http-server": "To leverage async resolving with webserver on AMPHP platform", "psr/http-message": "To use standard GraphQL server", "react/promise": "To leverage async resolving on React PHP platform"}, "time": "2025-07-10T08:58:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"GraphQL\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP port of GraphQL reference implementation", "homepage": "https://github.com/webonyx/graphql-php", "keywords": ["api", "graphql"], "support": {"issues": "https://github.com/webonyx/graphql-php/issues", "source": "https://github.com/webonyx/graphql-php/tree/v15.21.2"}, "funding": [{"url": "https://opencollective.com/webonyx-graphql-php", "type": "open_collective"}], "install-path": "../webonyx/graphql-php"}], "dev": false, "dev-package-names": []}