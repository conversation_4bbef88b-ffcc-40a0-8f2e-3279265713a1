<?php return array(
    'root' => array(
        'name' => 'silverstripe/installer',
        'pretty_version' => '5.4.0',
        'version' => '*******',
        'reference' => null,
        'type' => 'silverstripe-recipe',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'composer/ca-bundle' => array(
            'pretty_version' => '1.5.7',
            'version' => '*******',
            'reference' => 'd665d22c417056996c59019579f1967dfe5c1e82',
            'type' => 'library',
            'install_path' => __DIR__ . '/./ca-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/installers' => array(
            'pretty_version' => 'v2.3.0',
            'version' => '*******',
            'reference' => '12fb2dfe5e16183de69e784a7b84046c43d97e8e',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/./installers',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.3',
            'version' => '*******',
            'reference' => '4313d26ada5e0c4edfbd1dc481a92ff7bff91f12',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => 'd42c8731f0624ad6bdc8d3e5e9a4524f68801cfa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'embed/embed' => array(
            'pretty_version' => 'v4.4.17',
            'version' => '4.4.17.0',
            'reference' => 'b2ea091a5586c14ea5f2c5bf52fb0ef38e5aef87',
            'type' => 'library',
            'install_path' => __DIR__ . '/../embed/embed',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'intervention/image' => array(
            'pretty_version' => '2.7.2',
            'version' => '2.7.2.0',
            'reference' => '04be355f8d6734c826045d02a1079ad658322dad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/csv' => array(
            'pretty_version' => '9.24.1',
            'version' => '9.24.1.0',
            'reference' => 'e0221a3f16aa2a823047d59fab5809d552e29bc8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/csv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '3.30.0',
            'version' => '3.30.0.0',
            'reference' => '2203e3151755d874bb2943649dae1eb8533ac93e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-local' => array(
            'pretty_version' => '3.30.0',
            'version' => '3.30.0.0',
            'reference' => '6691915f77c7fb69adfb87dcd550052dc184ee10',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-local',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.16.0',
            'version' => '1.16.0.0',
            'reference' => '2d6702ff215bf922936ccc1ad31007edc76451b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'm1/env' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '5c296e3e13450a207e12b343f3af1d7ab569f6f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../m1/env',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'marcj/topsort' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => '972f58e42b5f110a0a1d8433247f65248abcfd5c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../marcj/topsort',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '2.9.0.0',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ml/iri' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'cbd44fa913e00ea624241b38cefaa99da8d71341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ml/iri/ML/IRI',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ml/json-ld' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'reference' => '537e68e87a6bce23e57c575cd5dcac1f67ce25d8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ml/json-ld',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '3.9.0',
            'version' => '3.9.0.0',
            'reference' => '10d85740180ecba7896c87e06a166e0c95a0e3b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v4.19.4',
            'version' => '4.19.4.0',
            'reference' => '715f4d25e225bc47b293a8b997fe6ce99bf987d2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'oscarotero/html-parser' => array(
            'pretty_version' => 'v0.1.8',
            'version' => '0.1.8.0',
            'reference' => '10f3219267a365d9433f2f7d1694209c9d436c8d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../oscarotero/html-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.0.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '4.0.6',
            'version' => '*******',
            'reference' => 'ba01945089c3a293b01ba9badc29ad55b106b0bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe-themes/simple' => array(
            'pretty_version' => '3.3.2',
            'version' => '*******',
            'reference' => 'ec62a3b15ad5dc8f5b37338325811cba63c1d564',
            'type' => 'silverstripe-theme',
            'install_path' => __DIR__ . '/../../themes/simple',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/admin' => array(
            'pretty_version' => '2.4.8',
            'version' => '*******',
            'reference' => 'b68f641a3baccca8eb29ed31fbd092bbca7b014a',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/admin',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/asset-admin' => array(
            'pretty_version' => '2.4.3',
            'version' => '*******',
            'reference' => '271ccb8ce780c1e5dc9b20eea193fb6b62ab3608',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/asset-admin',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/assets' => array(
            'pretty_version' => '2.4.2',
            'version' => '*******',
            'reference' => '815fe1c78bd1d470713651c65fa8968845f3cc21',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/assets',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/campaign-admin' => array(
            'pretty_version' => '2.4.2',
            'version' => '*******',
            'reference' => '905555d30fa49a68f6161b25d9f37f4594d9fab9',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/campaign-admin',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/cms' => array(
            'pretty_version' => '5.4.3',
            'version' => '*******',
            'reference' => '9af9b9186a6dd45d4cc6d4c26f83ba8bba782b66',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/cms',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/config' => array(
            'pretty_version' => '2.2.1',
            'version' => '*******',
            'reference' => '1cbb24bea999342717aa7577752cbf04065b68ec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../silverstripe/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/errorpage' => array(
            'pretty_version' => '2.4.0',
            'version' => '*******',
            'reference' => '489f7d31fb53ed71e363a13b3d3ecc420fa8b78c',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/errorpage',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/event-dispatcher' => array(
            'pretty_version' => '1.0.1',
            'version' => '*******',
            'reference' => 'aea42ed97039f7262217eab4d8325579ac4be18b',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/framework' => array(
            'pretty_version' => '5.4.12',
            'version' => '********',
            'reference' => '831ea2bb23b4650c70105cb5ef5118a1c0dbd509',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/graphql' => array(
            'pretty_version' => '5.3.1',
            'version' => '*******',
            'reference' => '3a240b343af5294228c49dcacf669ea0f50329f1',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/graphql',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/installer' => array(
            'pretty_version' => '5.4.0',
            'version' => '*******',
            'reference' => null,
            'type' => 'silverstripe-recipe',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/login-forms' => array(
            'pretty_version' => '5.3.1',
            'version' => '*******',
            'reference' => '752394c33ba21724e74d4e2e124926b908f5c679',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/login-forms',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/mimevalidator' => array(
            'pretty_version' => '3.1.0',
            'version' => '*******',
            'reference' => 'f21448ebc831201867ca6d10ebc1ccc838864a97',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/mimevalidator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/recipe-cms' => array(
            'pretty_version' => '5.4.0',
            'version' => '*******',
            'reference' => '0db42df22bbe021bd8cd1999e80fc92901bd7424',
            'type' => 'silverstripe-recipe',
            'install_path' => __DIR__ . '/../silverstripe/recipe-cms',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/recipe-core' => array(
            'pretty_version' => '5.4.0',
            'version' => '*******',
            'reference' => '2e6af93b5a29d14bf7208e4fbabef2a1ee81f4b9',
            'type' => 'silverstripe-recipe',
            'install_path' => __DIR__ . '/../silverstripe/recipe-core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/recipe-plugin' => array(
            'pretty_version' => '2.1.0',
            'version' => '*******',
            'reference' => '57d785dba26370ad9b981871411930ccca1dbffd',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../silverstripe/recipe-plugin',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/reports' => array(
            'pretty_version' => '5.4.1',
            'version' => '*******',
            'reference' => '04dd08096ea5030e34784c8bd85e09516d47db40',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/reports',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/session-manager' => array(
            'pretty_version' => '2.3.3',
            'version' => '*******',
            'reference' => 'cbb3f34d8ecfb89dbe82da43d89d685ca219cb02',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/session-manager',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/siteconfig' => array(
            'pretty_version' => '5.4.1',
            'version' => '*******',
            'reference' => 'c08b86cbb3372c47edc7f52ca03a047452fce917',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/siteconfig',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/supported-modules' => array(
            'pretty_version' => '1.1.5',
            'version' => '*******',
            'reference' => 'd9975ae0ed1b92ca157d29e0a9d91fefcf7c40cd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../silverstripe/supported-modules',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/vendor-plugin' => array(
            'pretty_version' => '2.1.0',
            'version' => '*******',
            'reference' => 'b9c18fa8488188908935c6a71222b637f3cfe64b',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../silverstripe/vendor-plugin',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/versioned' => array(
            'pretty_version' => '2.4.3',
            'version' => '*******',
            'reference' => 'fb3085391c6596d2ed9aed00683965ddb349087a',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/versioned',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'silverstripe/versioned-admin' => array(
            'pretty_version' => '2.4.3',
            'version' => '*******',
            'reference' => 'e242940eabff298e2829a07366c1982966e80d67',
            'type' => 'silverstripe-vendormodule',
            'install_path' => __DIR__ . '/../silverstripe/versioned-admin',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sminnee/callbacklist' => array(
            'pretty_version' => '0.1.1',
            'version' => '*******',
            'reference' => '8c9f0a3a9f57aaa8cadb72eb579f5550a73abbc4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sminnee/callbacklist',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '********',
            'reference' => 'c88690befb8d4a85dc321fb78d677507f5eb141b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '*******',
            'reference' => '5d68a57d66910405e5c0b63d6f0af941e66fc868',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/cache-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0|3.0',
            ),
        ),
        'symfony/config' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => 'af5917a3b1571f54689e56677a3f06440d2fe4c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '*******',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/dom-crawler' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '********',
            'reference' => '22210aacb35dbadd772325d759d17bce2374a84d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dom-crawler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '*******',
            'reference' => '59eb412e93815df44f05f342958efa9f46b1e586',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '4856c9cf585d5a0313d8d35afd681a526f038dd3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v6.4.17',
            'version' => '6.4.17.0',
            'reference' => '1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '********',
            'reference' => '452d19f945ee41345fd8a50c18b60783546b7bd3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '********',
            'reference' => 'a480322ddf8e54de262c9bca31fdcbe26b553de5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => 'fec8aa5231f3904754955fad33c2db50594d22d1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '9614ac4d8061dc257ecc64cba1b140873dce8ad3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '6d857f4d76bd4b343eac26d6b539585d2bc56493',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '*******',
            'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '********',
            'reference' => 'de8afa521e04a5220e9e58a1dc99971ab7cac643',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '*******',
            'reference' => 'df210c7a2573f1913b2d17cc95f90f53a73d8f7d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/validator' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '********',
            'reference' => '6506760ab57e7cda5bde9cdaed736526162284bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v6.4.22',
            'version' => '6.4.22.0',
            'reference' => 'f28cf841f5654955c9f88ceaf4b9dc29571988a9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '********',
            'reference' => '93e29e0deb5f1b2e360adfb389a20d25eb81a27b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ua-parser/uap-php' => array(
            'pretty_version' => 'v3.9.14',
            'version' => '3.9.14.0',
            'reference' => 'b796c5ea5df588e65aeb4e2c6cce3811dec4fed6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ua-parser/uap-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webonyx/graphql-php' => array(
            'pretty_version' => 'v15.21.2',
            'version' => '15.21.2.0',
            'reference' => '1a7aac3b7f8d66fd52aa7d1b7eafad3dec1f9cb5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webonyx/graphql-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
