<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'UAParser\\' => array($vendorDir . '/ua-parser/uap-php/src'),
    'Symfony\\Polyfill\\Php83\\' => array($vendorDir . '/symfony/polyfill-php83'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\Idn\\' => array($vendorDir . '/symfony/polyfill-intl-idn'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Contracts\\Translation\\' => array($vendorDir . '/symfony/translation-contracts'),
    'Symfony\\Contracts\\Service\\' => array($vendorDir . '/symfony/service-contracts'),
    'Symfony\\Contracts\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher-contracts'),
    'Symfony\\Contracts\\Cache\\' => array($vendorDir . '/symfony/cache-contracts'),
    'Symfony\\Component\\Yaml\\' => array($vendorDir . '/symfony/yaml'),
    'Symfony\\Component\\VarExporter\\' => array($vendorDir . '/symfony/var-exporter'),
    'Symfony\\Component\\Validator\\' => array($vendorDir . '/symfony/validator'),
    'Symfony\\Component\\Translation\\' => array($vendorDir . '/symfony/translation'),
    'Symfony\\Component\\Mime\\' => array($vendorDir . '/symfony/mime'),
    'Symfony\\Component\\Mailer\\' => array($vendorDir . '/symfony/mailer'),
    'Symfony\\Component\\HttpFoundation\\' => array($vendorDir . '/symfony/http-foundation'),
    'Symfony\\Component\\Finder\\' => array($vendorDir . '/symfony/finder'),
    'Symfony\\Component\\Filesystem\\' => array($vendorDir . '/symfony/filesystem'),
    'Symfony\\Component\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher'),
    'Symfony\\Component\\DomCrawler\\' => array($vendorDir . '/symfony/dom-crawler'),
    'Symfony\\Component\\Config\\' => array($vendorDir . '/symfony/config'),
    'Symfony\\Component\\Cache\\' => array($vendorDir . '/symfony/cache'),
    'Sminnee\\CallbackList\\' => array($vendorDir . '/sminnee/callbacklist/src'),
    'SilverStripe\\i18n\\Tests\\' => array($vendorDir . '/silverstripe/framework/tests/php/i18n'),
    'SilverStripe\\i18n\\' => array($vendorDir . '/silverstripe/framework/src/i18n'),
    'SilverStripe\\View\\Tests\\' => array($vendorDir . '/silverstripe/framework/tests/php/View'),
    'SilverStripe\\View\\' => array($vendorDir . '/silverstripe/framework/src/View'),
    'SilverStripe\\Versioned\\Tests\\' => array($vendorDir . '/silverstripe/versioned/tests/php'),
    'SilverStripe\\Versioned\\' => array($vendorDir . '/silverstripe/versioned/src'),
    'SilverStripe\\VersionedAdmin\\Tests\\' => array($vendorDir . '/silverstripe/versioned-admin/tests'),
    'SilverStripe\\VersionedAdmin\\' => array($vendorDir . '/silverstripe/versioned-admin/src'),
    'SilverStripe\\VendorPlugin\\Tests\\' => array($vendorDir . '/silverstripe/vendor-plugin/tests'),
    'SilverStripe\\VendorPlugin\\' => array($vendorDir . '/silverstripe/vendor-plugin/src'),
    'SilverStripe\\SupportedModules\\Tests\\' => array($vendorDir . '/silverstripe/supported-modules/tests'),
    'SilverStripe\\SupportedModules\\' => array($vendorDir . '/silverstripe/supported-modules/src'),
    'SilverStripe\\SiteConfig\\Tests\\' => array($vendorDir . '/silverstripe/siteconfig/tests/php'),
    'SilverStripe\\SiteConfig\\' => array($vendorDir . '/silverstripe/siteconfig/code'),
    'SilverStripe\\SessionManager\\Tests\\Behat\\Context\\' => array($vendorDir . '/silverstripe/session-manager/tests/behat/src'),
    'SilverStripe\\SessionManager\\Tests\\' => array($vendorDir . '/silverstripe/session-manager/tests/php'),
    'SilverStripe\\SessionManager\\' => array($vendorDir . '/silverstripe/session-manager/src'),
    'SilverStripe\\Security\\Tests\\' => array($vendorDir . '/silverstripe/framework/tests/php/Security'),
    'SilverStripe\\Security\\' => array($vendorDir . '/silverstripe/framework/src/Security'),
    'SilverStripe\\Reports\\Tests\\' => array($vendorDir . '/silverstripe/reports/tests'),
    'SilverStripe\\Reports\\' => array($vendorDir . '/silverstripe/reports/code'),
    'SilverStripe\\RecipePlugin\\' => array($vendorDir . '/silverstripe/recipe-plugin/src'),
    'SilverStripe\\ORM\\Tests\\' => array($vendorDir . '/silverstripe/framework/tests/php/ORM'),
    'SilverStripe\\ORM\\' => array($vendorDir . '/silverstripe/framework/src/ORM'),
    'SilverStripe\\MimeValidator\\Tests\\' => array($vendorDir . '/silverstripe/mimevalidator/tests'),
    'SilverStripe\\MimeValidator\\' => array($vendorDir . '/silverstripe/mimevalidator/src'),
    'SilverStripe\\LoginForms\\Tests\\' => array($vendorDir . '/silverstripe/login-forms/tests'),
    'SilverStripe\\LoginForms\\' => array($vendorDir . '/silverstripe/login-forms/src'),
    'SilverStripe\\Logging\\Tests\\' => array($vendorDir . '/silverstripe/framework/tests/php/Logging'),
    'SilverStripe\\Logging\\' => array($vendorDir . '/silverstripe/framework/src/Logging'),
    'SilverStripe\\GraphQL\\Tests\\' => array($vendorDir . '/silverstripe/graphql/tests'),
    'SilverStripe\\GraphQL\\' => array($vendorDir . '/silverstripe/graphql/src'),
    'SilverStripe\\Framework\\Tests\\Behaviour\\' => array($vendorDir . '/silverstripe/framework/tests/behat/src'),
    'SilverStripe\\Forms\\Tests\\' => array($vendorDir . '/silverstripe/framework/tests/php/Forms'),
    'SilverStripe\\Forms\\' => array($vendorDir . '/silverstripe/framework/src/Forms'),
    'SilverStripe\\EventDispatcher\\Tests\\' => array($vendorDir . '/silverstripe/event-dispatcher/tests'),
    'SilverStripe\\EventDispatcher\\' => array($vendorDir . '/silverstripe/event-dispatcher/src'),
    'SilverStripe\\ErrorPage\\Tests\\' => array($vendorDir . '/silverstripe/errorpage/tests'),
    'SilverStripe\\ErrorPage\\' => array($vendorDir . '/silverstripe/errorpage/src'),
    'SilverStripe\\Dev\\Tests\\' => array($vendorDir . '/silverstripe/framework/tests/php/Dev'),
    'SilverStripe\\Dev\\' => array($vendorDir . '/silverstripe/framework/src/Dev'),
    'SilverStripe\\Core\\Tests\\' => array($vendorDir . '/silverstripe/framework/tests/php/Core'),
    'SilverStripe\\Core\\' => array($vendorDir . '/silverstripe/framework/src/Core'),
    'SilverStripe\\Control\\Tests\\' => array($vendorDir . '/silverstripe/framework/tests/php/Control'),
    'SilverStripe\\Control\\' => array($vendorDir . '/silverstripe/framework/src/Control'),
    'SilverStripe\\Config\\Tests\\' => array($vendorDir . '/silverstripe/config/tests'),
    'SilverStripe\\Config\\' => array($vendorDir . '/silverstripe/config/src'),
    'SilverStripe\\CampaignAdmin\\Tests\\Behat\\Context\\' => array($vendorDir . '/silverstripe/campaign-admin/tests/behat/src'),
    'SilverStripe\\CampaignAdmin\\Tests\\' => array($vendorDir . '/silverstripe/campaign-admin/tests/php'),
    'SilverStripe\\CampaignAdmin\\' => array($vendorDir . '/silverstripe/campaign-admin/src'),
    'SilverStripe\\CMS\\Tests\\Behaviour\\' => array($vendorDir . '/silverstripe/cms/tests/behat/src'),
    'SilverStripe\\CMS\\Tests\\' => array($vendorDir . '/silverstripe/cms/code/php'),
    'SilverStripe\\CMS\\' => array($vendorDir . '/silverstripe/cms/code', $vendorDir . '/silverstripe/cms/_legacy'),
    'SilverStripe\\Assets\\Tests\\' => array($vendorDir . '/silverstripe/assets/tests/php'),
    'SilverStripe\\Assets\\' => array($vendorDir . '/silverstripe/assets/src'),
    'SilverStripe\\AssetAdmin\\Tests\\Behat\\Context\\' => array($vendorDir . '/silverstripe/asset-admin/tests/behat/src'),
    'SilverStripe\\AssetAdmin\\Tests\\' => array($vendorDir . '/silverstripe/asset-admin/tests/php'),
    'SilverStripe\\AssetAdmin\\' => array($vendorDir . '/silverstripe/asset-admin/code'),
    'SilverStripe\\Admin\\Tests\\Behat\\Context\\' => array($vendorDir . '/silverstripe/admin/tests/behat/src'),
    'SilverStripe\\Admin\\Tests\\' => array($vendorDir . '/silverstripe/admin/tests/php'),
    'SilverStripe\\Admin\\' => array($vendorDir . '/silverstripe/admin/code'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'Psr\\EventDispatcher\\' => array($vendorDir . '/psr/event-dispatcher/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Psr\\Cache\\' => array($vendorDir . '/psr/cache/src'),
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'Masterminds\\' => array($vendorDir . '/masterminds/html5/src'),
    'ML\\JsonLD\\' => array($vendorDir . '/ml/json-ld'),
    'MJS\\TopSort\\Tests\\' => array($vendorDir . '/marcj/topsort/tests/Tests'),
    'MJS\\TopSort\\' => array($vendorDir . '/marcj/topsort/src'),
    'M1\\Env\\' => array($vendorDir . '/m1/env/src'),
    'League\\MimeTypeDetection\\' => array($vendorDir . '/league/mime-type-detection/src'),
    'League\\Flysystem\\Local\\' => array($vendorDir . '/league/flysystem-local'),
    'League\\Flysystem\\' => array($vendorDir . '/league/flysystem/src'),
    'League\\Csv\\' => array($vendorDir . '/league/csv/src'),
    'Intervention\\Image\\' => array($vendorDir . '/intervention/image/src/Intervention/Image'),
    'HtmlParser\\' => array($vendorDir . '/oscarotero/html-parser/src'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'GraphQL\\' => array($vendorDir . '/webonyx/graphql-php/src'),
    'Embed\\' => array($vendorDir . '/embed/embed/src'),
    'Egulias\\EmailValidator\\' => array($vendorDir . '/egulias/email-validator/src'),
    'Doctrine\\Common\\Lexer\\' => array($vendorDir . '/doctrine/lexer/src'),
    'Composer\\Semver\\' => array($vendorDir . '/composer/semver/src'),
    'Composer\\Installers\\' => array($vendorDir . '/composer/installers/src/Composer/Installers'),
    'Composer\\CaBundle\\' => array($vendorDir . '/composer/ca-bundle/src'),
);
