{"name": "marcj/topsort", "type": "library", "description": "High-Performance TopSort/Dependency resolving algorithm", "keywords": ["topological sort", "topsort", "dependency resolving"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9", "symfony/console": "~2.5 || ~3.0 || ~4.0", "codeclimate/php-test-reporter": "dev-master"}, "autoload": {"psr-4": {"MJS\\TopSort\\": "src/", "MJS\\TopSort\\Tests\\": "tests/Tests/"}}, "extra": {"branch-alias": {"dev-master": "2.x-dev"}}}