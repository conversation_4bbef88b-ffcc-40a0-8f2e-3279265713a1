{"name": "oscarotero/html-parser", "type": "library", "description": "Parse html strings to DOMDocument", "keywords": ["html", "parser", "dom"], "homepage": "https://github.com/oscarotero/html-parser", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/oscarotero/html-parser/issues"}, "require": {"php": "^7.2 || ^8"}, "autoload": {"psr-4": {"HtmlParser\\": "src"}}, "autoload-dev": {"psr-4": {"HtmlParser\\Tests\\": "tests"}}, "require-dev": {"phpunit/phpunit": "^8.0", "friendsofphp/php-cs-fixer": "^2.11"}, "scripts": {"test": "phpunit", "cs-fix": "php-cs-fixer fix ."}}