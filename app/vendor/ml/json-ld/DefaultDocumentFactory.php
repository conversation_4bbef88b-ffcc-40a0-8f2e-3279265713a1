<?php

/*
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace ML\JsonLD;

/**
 * DefaultDocumentFactory creates new Documents
 *
 * @see Document
 *
 * <AUTHOR> <<EMAIL>>
 */
class DefaultDocumentFactory implements DocumentFactoryInterface
{
    /**
     * {@inheritdoc}
     */
    public function createDocument($iri = null)
    {
        return new Document($iri);
    }
}
