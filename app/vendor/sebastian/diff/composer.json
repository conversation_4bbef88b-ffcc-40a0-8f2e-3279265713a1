{"name": "sebastian/diff", "description": "Diff implementation", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "homepage": "https://github.com/sebastian<PERSON>mann/diff", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "prefer-stable": true, "config": {"platform": {"php": "7.3.0"}, "optimize-autoloader": true, "sort-packages": true}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/"]}, "extra": {"branch-alias": {"dev-master": "4.0-dev"}}}