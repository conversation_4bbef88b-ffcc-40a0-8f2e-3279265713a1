{"name": "sminnee/callbacklist", "description": "PHP class that manages a list of callbacks", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.1 || ^8"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9", "squizlabs/php_codesniffer": "^3.5", "slevomat/coding-standard": "^6.4", "php-parallel-lint/php-parallel-lint": "^1.2", "php-parallel-lint/php-console-highlighter": "^0.5.0", "phpstan/phpstan-strict-rules": "^0.12.5"}, "autoload": {"psr-4": {"Sminnee\\CallbackList\\": "src/"}}, "scripts": {"phpcs": "vendor/bin/phpcs --extensions=php --tab-width=4 -sp src tests", "phpcbf": "vendor/bin/phpcbf --extensions=php --tab-width=4 -sp src tests", "lint": "vendor/bin/parallel-lint src/ tests/", "phpstan": "vendor/bin/phpstan analyse --level=8 -c tests/phpstan.neon src/", "check": "composer lint && composer phpcs && composer phpstan", "test": "vendor/bin/phpunit", "test-coverage": "phpdbg -qrr vendor/bin/phpunit tests --coverage-clover=coverage.xml '''' flush=all"}}