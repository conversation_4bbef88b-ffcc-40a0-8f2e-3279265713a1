language: php
matrix:
  fast_finish: true
  include:
    -
      php: 7.1
      env:
        - PHPUNIT_TEST=1
        - COMPOSER_ARG=--prefer-lowest
    -
      php: 7.4
      env:
        - PHPUNIT_COVERAGE_TEST=1
    -
      php: nightly
      env:
        - PHPUNIT_TEST=1
        - COMPOSER_ARG=--ignore-platform-reqs
    -
      php: 7.4
      env:
        - LINT_CHECK=1
        - PHPCS_TEST=1
        - DUPLICATE_CODE_CHECK=1
        - PHPSTAN_TEST=1

before_script:
  - 'composer validate'
  - 'composer update $COMPOSER_ARG --no-interaction --no-progress --no-suggest --optimize-autoloader --verbose --profile'
  - 'if [[ $DUPLICATE_CODE_CHECK ]]; then sudo apt remove -y nodejs && curl -sL https://deb.nodesource.com/setup_14.x -o nodesource_setup.sh && sudo bash nodesource_setup.sh && sudo apt install -y build-essential nodejs  && npm install jscpd@3.2.1  ;fi'

script:
  - 'if [[ $LINT_CHECK ]]; then composer lint; fi'
  - 'if [[ $PHPCS_TEST ]]; then composer phpcs; fi'
  - 'if [[ $PHPSTAN_TEST ]]; then composer phpstan; fi'
  - 'if [[ $PHPUNIT_COVERAGE_TEST ]]; then composer test-coverage; fi'
  - 'if [[ $PHPUNIT_TEST ]]; then composer test; fi'
  - 'if [[ $DUPLICATE_CODE_CHECK ]]; then node_modules/jscpd/bin/jscpd -t 1 src; fi'

after_script:
  - 'if [[ $PHPUNIT_COVERAGE_TEST ]]; then bash <(curl -s https://codecov.io/bash) -f coverage.xml; fi'
