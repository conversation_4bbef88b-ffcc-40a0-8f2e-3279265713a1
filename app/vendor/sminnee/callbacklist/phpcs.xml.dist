<?xml version="1.0"?>
<ruleset name="YOUR_PROJECT">
	<config name="installed_paths" value="../../slevomat/coding-standard"/><!-- relative path from PHPCS source location -->

	<rule ref="PSR2"/>
	<rule ref="SlevomatCodingStandard.Arrays.TrailingArrayComma"/>
	<rule ref="SlevomatCodingStandard.Arrays.MultiLineArrayEndBracketPlacement"/>
	<rule ref="SlevomatCodingStandard.Arrays.DisallowImplicitArrayCreation"/>
	<rule ref="SlevomatCodingStandard.Arrays.SingleLineArrayWhitespace"/>


	<rule ref="SlevomatCodingStandard.Classes.TraitUseDeclaration"/>
	<rule ref="SlevomatCodingStandard.Classes.RequireSingleLineMethodSignature"/>
	<rule ref="SlevomatCodingStandard.Classes.DisallowMultiPropertyDefinition"/>
	<rule ref="SlevomatCodingStandard.Classes.SuperfluousExceptionNaming"/>
	<rule ref="SlevomatCodingStandard.Classes.ModernClassNameReference"/>
	<rule ref="SlevomatCodingStandard.Classes.ConstantSpacing"/>
	<rule ref="SlevomatCodingStandard.Classes.ClassStructure"/>
	<rule ref="SlevomatCodingStandard.Classes.ClassConstantVisibility"/>
	<rule ref="SlevomatCodingStandard.Classes.RequireMultiLineMethodSignature"/>
	<rule ref="SlevomatCodingStandard.Classes.SuperfluousInterfaceNaming"/>
	<rule ref="SlevomatCodingStandard.Classes.PropertySpacing"/>
	<!-- <rule ref="SlevomatCodingStandard.Classes.EmptyLinesAroundClassBraces"/> -->
	<rule ref="SlevomatCodingStandard.Classes.DisallowLateStaticBindingForConstants"/>
	<rule ref="SlevomatCodingStandard.Classes.SuperfluousAbstractClassNaming"/>
	<rule ref="SlevomatCodingStandard.Classes.ParentCallSpacing"/>
	<!-- <rule ref="SlevomatCodingStandard.Classes.UnusedPrivateElements"/> -->
	<rule ref="SlevomatCodingStandard.Classes.TraitUseSpacing"/>
	<rule ref="SlevomatCodingStandard.Classes.MethodSpacing">
		<properties>
			<property name="minLinesCount" value="2"/>
			<property name="maxLinesCount" value="2"/>
		</properties>
	</rule>
	<rule ref="SlevomatCodingStandard.Classes.SuperfluousTraitNaming"/>
	<rule ref="SlevomatCodingStandard.Classes.DisallowMultiConstantDefinition"/>

	<!-- this fails even it is the only rule
	<rule ref="SlevomatCodingStandard.Classes.ClassMemberSpacing"/>
	-->

	<rule ref="SlevomatCodingStandard.Classes.UselessLateStaticBinding"/>


	<rule ref="SlevomatCodingStandard.Commenting.UselessInheritDocComment" />
	<!-- <rule ref="SlevomatCodingStandard.Commenting.DisallowOneLinePropertyDocComment" /> -->
	<rule ref="SlevomatCodingStandard.Commenting.UselessFunctionDocComment" />
	<rule ref="SlevomatCodingStandard.Commenting.RequireOneLineDocComment" />
	<rule ref="SlevomatCodingStandard.Commenting.ForbiddenComments" />
	<rule ref="SlevomatCodingStandard.Commenting.RequireOneLinePropertyDocComment" />
	<rule ref="SlevomatCodingStandard.Commenting.EmptyComment" />
	<rule ref="SlevomatCodingStandard.Commenting.DocCommentSpacing" />
	<rule ref="SlevomatCodingStandard.Commenting.InlineDocCommentDeclaration" />
	<rule ref="SlevomatCodingStandard.Commenting.DisallowCommentAfterCode" />
	<rule ref="SlevomatCodingStandard.Commenting.ForbiddenAnnotations" />


	<rule ref="SlevomatCodingStandard.ControlStructures.DisallowYodaComparison"/>
	<rule ref="SlevomatCodingStandard.ControlStructures.UselessIfConditionWithReturn"/>

	<rule ref="SlevomatCodingStandard.ControlStructures.DisallowShortTernaryOperator"/>
	<rule ref="SlevomatCodingStandard.ControlStructures.RequireShortTernaryOperator"/>
	<rule ref="SlevomatCodingStandard.ControlStructures.RequireMultiLineTernaryOperator"/>
	<rule ref="SlevomatCodingStandard.ControlStructures.DisallowContinueWithoutIntegerOperandInSwitch"/>
	<rule ref="SlevomatCodingStandard.ControlStructures.EarlyExit"/>


	<rule ref="SlevomatCodingStandard.ControlStructures.AssignmentInCondition"/>
	<rule ref="SlevomatCodingStandard.ControlStructures.NewWithParentheses"/>
	<!-- <rule ref="SlevomatCodingStandard.ControlStructures.RequireNullCoalesceOperator"/> -->
	<!-- <rule ref="SlevomatCodingStandard.ControlStructures.RequireYodaComparison"/> -->
	<rule ref="SlevomatCodingStandard.ControlStructures.UselessTernaryOperator"/>
	<rule ref="SlevomatCodingStandard.ControlStructures.RequireNullCoalesceEqualOperator"/>
	<!-- <rule ref="SlevomatCodingStandard.ControlStructures.NewWithoutParentheses"/> -->
	<!-- <rule ref="SlevomatCodingStandard.ControlStructures.BlockControlStructureSpacing"/> -->


	<rule ref="SlevomatCodingStandard.ControlStructures.JumpStatementsSpacing"/>
	<rule ref="SlevomatCodingStandard.ControlStructures.RequireTernaryOperator"/>
	<rule ref="SlevomatCodingStandard.ControlStructures.LanguageConstructWithParentheses"/>
	<rule ref="SlevomatCodingStandard.ControlStructures.DisallowEmpty"/>

	<!-- <rule ref="SlevomatCodingStandard.Files.TypeNameMatchesFileName"/> -->
	<rule ref="SlevomatCodingStandard.Files.LineLength"/>

	<rule ref="SlevomatCodingStandard.Exceptions.ReferenceThrowableOnly"/>
	<rule ref="SlevomatCodingStandard.Exceptions.DeadCatch"/>

	<rule ref="SlevomatCodingStandard.Functions.DisallowArrowFunction"/>
	<!-- <rule ref="SlevomatCodingStandard.Functions.RequireArrowFunction"/> -->
	<rule ref="SlevomatCodingStandard.Functions.DisallowEmptyFunction"/>
	<!-- <rule ref="SlevomatCodingStandard.Functions.TrailingCommaInCall"/> -->
	<rule ref="SlevomatCodingStandard.Functions.StrictCall"/>
	<rule ref="SlevomatCodingStandard.Functions.UnusedInheritedVariablePassedToClosure"/>
	<rule ref="SlevomatCodingStandard.Functions.UselessParameterDefaultValue"/>
	<rule ref="SlevomatCodingStandard.Functions.UnusedParameter"/>
	<rule ref="SlevomatCodingStandard.Functions.ArrowFunctionDeclaration"/>
	<rule ref="SlevomatCodingStandard.Functions.StaticClosure"/>


	<rule ref="SlevomatCodingStandard.Namespaces.FullyQualifiedClassNameInAnnotation"/>
	<rule ref="SlevomatCodingStandard.Namespaces.UseSpacing"/>
	<rule ref="SlevomatCodingStandard.Namespaces.MultipleUsesPerLine"/>
<!--	<rule ref="SlevomatCodingStandard.Namespaces.FullyQualifiedExceptions"/> -->
<!--	<rule ref="SlevomatCodingStandard.Namespaces.UseOnlyWhitelistedNamespaces"/> -->
	<rule ref="SlevomatCodingStandard.Namespaces.UseDoesNotStartWithBackslash"/>
<!--	<rule ref="SlevomatCodingStandard.Namespaces.ReferenceUsedNamesOnly"/> -->
	<rule ref="SlevomatCodingStandard.Namespaces.UnusedUses"/>
	<rule ref="SlevomatCodingStandard.Namespaces.AlphabeticallySortedUses"/>
	<rule ref="SlevomatCodingStandard.Namespaces.DisallowGroupUse"/>
	<rule ref="SlevomatCodingStandard.Namespaces.FullyQualifiedGlobalConstants"/>
	<rule ref="SlevomatCodingStandard.Namespaces.FullyQualifiedClassNameAfterKeyword"/>
	<rule ref="SlevomatCodingStandard.Namespaces.FullyQualifiedGlobalFunctions"/>
	<rule ref="SlevomatCodingStandard.Namespaces.NamespaceDeclaration"/>
	<rule ref="SlevomatCodingStandard.Namespaces.UselessAlias"/>
	<rule ref="SlevomatCodingStandard.Namespaces.RequireOneNamespaceInFile"/>
	<rule ref="SlevomatCodingStandard.Namespaces.NamespaceSpacing"/>
	<rule ref="SlevomatCodingStandard.Namespaces.UseFromSameNamespace"/>

	<rule ref="SlevomatCodingStandard.Numbers.DisallowNumericLiteralSeparator"/>
	<!-- <rule ref="SlevomatCodingStandard.Numbers.RequireNumericLiteralSeparator"/> -->


	<rule ref="SlevomatCodingStandard.Operators.RequireOnlyStandaloneIncrementAndDecrementOperators"/>
	<rule ref="SlevomatCodingStandard.Operators.RequireCombinedAssignmentOperator"/>
	<rule ref="SlevomatCodingStandard.Operators.SpreadOperatorSpacing"/>
	<rule ref="SlevomatCodingStandard.Operators.DisallowEqualOperators"/>
	<rule ref="SlevomatCodingStandard.Operators.NegationOperatorSpacing"/>
	<!-- <rule ref="SlevomatCodingStandard.Operators.DisallowIncrementAndDecrementOperators"/> -->

	<rule ref="SlevomatCodingStandard.TypeHints.ParameterTypeHintSpacing"/>
	<rule ref="SlevomatCodingStandard.TypeHints.NullTypeHintOnLastPosition"/>
	<rule ref="SlevomatCodingStandard.TypeHints.UselessConstantTypeHint"/>
	<rule ref="SlevomatCodingStandard.TypeHints.ParameterTypeHint">
		<properties>
			<!-- this needs PHP 7.2_ -->
			<property name="enableObjectTypeHint" value="false"/>
		</properties>
	</rule>
	<rule ref="SlevomatCodingStandard.TypeHints.NullableTypeForNullDefaultValue"/>
	<!-- This is not compatible with PHP < 7.4
	<rule ref="SlevomatCodingStandard.TypeHints.PropertyTypeHint"/>
	-->
	<rule ref="SlevomatCodingStandard.TypeHints.ReturnTypeHintSpacing"/>
	<rule ref="SlevomatCodingStandard.TypeHints.DisallowMixedTypeHint"/>
	<rule ref="SlevomatCodingStandard.TypeHints.DisallowArrayTypeHintSyntax"/>
	<rule ref="SlevomatCodingStandard.TypeHints.LongTypeHints"/>
	<rule ref="SlevomatCodingStandard.TypeHints.DeclareStrictTypes"/>
	<rule ref="SlevomatCodingStandard.TypeHints.ReturnTypeHint"/>
	<rule ref="SlevomatCodingStandard.TypeHints.PropertyTypeHintSpacing"/>

	<rule ref="SlevomatCodingStandard.Variables.DuplicateAssignmentToVariable"/>
	<rule ref="SlevomatCodingStandard.Variables.UselessVariable"/>
	<rule ref="SlevomatCodingStandard.Variables.UnusedVariable"/>
	<rule ref="SlevomatCodingStandard.Variables.DisallowSuperGlobalVariable"/>

	<rule ref="SlevomatCodingStandard.Whitespaces.DuplicateSpaces"/>

</ruleset>
