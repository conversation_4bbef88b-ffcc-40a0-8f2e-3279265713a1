{"name": "silverstripe/vendor-plugin", "description": "Allows vendor modules to expose directories to the webroot", "type": "composer-plugin", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"SilverStripe\\VendorPlugin\\": "src/", "SilverStripe\\VendorPlugin\\Tests\\": "tests/"}}, "extra": {"class": "SilverStripe\\VendorPlugin\\VendorPlugin"}, "scripts": {"lint": "phpcs src/ tests/", "lint-clean": "phpcbf src/ tests/"}, "require": {"php": "^8.1", "composer/installers": "^2", "composer-plugin-api": "^2"}, "require-dev": {"composer/composer": "^2", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7"}}