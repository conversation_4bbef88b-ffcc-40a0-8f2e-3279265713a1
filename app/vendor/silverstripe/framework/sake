#!/usr/bin/env bash

# Check for an argument
if [ ${1:-""} = "" ]; then
	echo "SilverStripe Sake

Usage: $0 (command-url) (params)
Executes a SilverStripe command"
	exit 1
fi

command -v which >/dev/null 2>&1
if [ $? -ne 0 ]; then
  echo "Error: sake requires the 'which' command to operate." >&2
  exit 1
fi

# find the silverstripe installation, looking first at sake
# bin location, but falling back to current directory
sakedir=`dirname $0`
directory="$PWD"
if [ -f "$sakedir/cli-script.php" ]; then
    # Calling sake from vendor/silverstripe/framework/sake
	framework="$sakedir"
	base="$sakedir/../../.."
elif [ -f "$sakedir/../silverstripe/framework/cli-script.php" ]; then
    # Calling sake from vendor/bin/sake
	framework="$sakedir/../silverstripe/framework"
	base="$sakedir/../.."
elif [ -f "$directory/vendor/silverstripe/framework/cli-script.php" ]; then
    # Vendor framework (from base) if sake installed globally
    framework="$directory/vendor/silverstripe/framework"
    base=.
elif [ -f "$directory/framework/cli-script.php" ]; then
    # Legacy directory (from base) if sake installed globally
    framework="$directory/framework"
    base=.
else
    echo "Can't find cli-script.php in $sakedir"
    exit 1
fi

# Find the PHP binary
for candidatephp in php php5; do
	if [ "`which $candidatephp 2>/dev/null`" -a -f "`which $candidatephp 2>/dev/null`" ]; then
		php=`which $candidatephp 2>/dev/null`
		break
	fi
done
if [ "$php" = "" ]; then
	echo "Can't find any php binary"
	exit 2
fi

################################################################################################
## Installation to /usr/bin

if [ "$1" = "installsake" ]; then
	echo "Installing sake to /usr/local/bin..."
	rm -rf /usr/local/bin/sake
	cp $0 /usr/local/bin
	exit 0
fi

################################################################################################
## Process control

if [ "$1" = "-start" ]; then
	if [ "`which daemon`" = "" ]; then
		echo "You need to install the 'daemon' tool.  In debian, go 'sudo apt-get install daemon'"
		exit 1
	fi

	if [ ! -f $base/$2.pid ]; then
		echo "Starting service $2 $3"
		touch $base/$2.pid
		pidfile=`realpath $base/$2.pid`

		outlog=$base/$2.log
		errlog=$base/$2.err

		echo "Logging to $outlog"

		sake=`realpath $0`
		base=`realpath $base`

		# if third argument is not explicitly given, copy from second argument
		if [ "$3" = "" ]; then
			url=$2
		else
			url=$3
		fi

		processname=$2

		daemon -n $processname -r -D $base --pidfile=$pidfile --stdout=$outlog --stderr=$errlog $sake $url
	else
		echo "Service $2 seems to already be running"
	fi
		exit 0
fi

if [ "$1" = "-stop" ]; then
	pidfile=$base/$2.pid
	if [ -f $pidfile ]; then
		echo "Stopping service $2"

		kill -KILL `cat $pidfile`
		unlink $pidfile
	else
		echo "Service $2 doesn't seem to be running."
	fi
	exit 0
fi

################################################################################################
## Basic execution

"$php" "$framework/cli-script.php" "${@}"
