## Silverstripe Core Recipe

[![CI](https://github.com/silverstripe/recipe-core/actions/workflows/ci.yml/badge.svg)](https://github.com/silverstripe/recipe-core/actions/workflows/ci.yml)
[![Silverstripe supported module](https://img.shields.io/badge/silverstripe-supported-0071C4.svg)](https://www.silverstripe.org/software/addons/silverstripe-commercially-supported-module-list/)

Base framework-only recipe for a Silverstripe ([http://silverstripe.org](http://silverstripe.org)) installation.
This includes the core modules:

 * [framework](http://github.com/silverstripe/silverstripe-framework): Module including the base framework
 * [config](https://github.com/silverstripe/silverstripe-config): Core config library
 * [assets](http://github.com/silverstripe/silverstripe-assets): Filesystem module

This can be either added to an existing project or used as a project base for creating a
basic framework-only install.

See the [recipe plugin](https://github.com/silverstripe/recipe-plugin) page for instructions on how
Silverstripe recipes work.
