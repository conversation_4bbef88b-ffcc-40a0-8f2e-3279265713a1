<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="SilverStripe">
    <description>CodeSniffer ruleset for SilverStripe coding conventions.</description>

    <file>src</file>
    <file>tests</file>

    <!-- base rules are PSR-2 -->
    <rule ref="PSR2" >
        <!-- Allow non camel cased method names -->
        <exclude name="PSR1.Methods.CamelCapsMethodName.NotCamelCaps"/>
    </rule>
</ruleset>
