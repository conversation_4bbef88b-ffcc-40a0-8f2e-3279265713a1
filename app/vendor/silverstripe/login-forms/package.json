{"name": "login-forms", "version": "5.0.0", "description": "A collection of templates for the CMS login screens", "engines": {"node": "^18.x"}, "scripts": {"build": "yarn && yarn lint && rm -rf client/dist/* && NODE_ENV=production webpack --mode production --bail --progress", "dev": "NODE_ENV=development webpack --progress", "watch": "yarn && NODE_ENV=development webpack --watch --progress", "css": "WEBPACK_CHILD=css npm run build", "lint": "stylelint client/src"}, "repository": {"type": "git", "url": "git+https://github.com/silverstripe/login-forms.git"}, "author": "SilverStripe Ltd", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/silverstripe/login-forms/issues"}, "homepage": "https://github.com/silverstripe/login-forms#readme", "dependencies": {"bootstrap": "^4.6.2", "jquery": "^3.6.1", "popper.js": "^1.14.3"}, "devDependencies": {"@babel/core": "^7.20.5", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@silverstripe/eslint-config": "^1.3.0", "@silverstripe/webpack-config": "^2.1.0", "babel-loader": "^9.1.0", "css-loader": "^6.7.2", "eslint": "^8.29.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-loader": "^4.0.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-react": "^7.31.11", "webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "resolutions": {"colors": "1.4.0"}, "browserslist": ["defaults"]}