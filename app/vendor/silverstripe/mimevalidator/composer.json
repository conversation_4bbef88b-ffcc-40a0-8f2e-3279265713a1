{"name": "silverstripe/mimevalidator", "description": "Checks uploaded file content roughly matches a known MIME type for the file extension.", "type": "silverstripe-vendormodule", "keywords": ["silverstripe", "mime", "upload", "validator", "fileinfo", "cwp"], "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "ext-fileinfo": "*", "silverstripe/framework": "^5"}, "require-dev": {"phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7", "nikic/php-parser": "^4.15.0", "monolog/monolog": "^3.2.0", "silverstripe/standards": "^1", "phpstan/extension-installer": "^1.3"}, "autoload": {"psr-4": {"SilverStripe\\MimeValidator\\": "src/", "SilverStripe\\MimeValidator\\Tests\\": "tests/"}}, "extra": [], "minimum-stability": "dev", "prefer-stable": true}