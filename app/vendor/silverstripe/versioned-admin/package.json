{"name": "silverstripe-versioned-admin", "version": "0.0.0", "description": "Versioned management for the SilverStripe CMS", "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/silverstripe/silverstripe-versioned-admin.git"}, "homepage": "https://github.com/silverstripe/silverstripe-versioned-admin", "bugs": {"url": "https://github.com/silverstripe/silverstripe-versioned-admin/issues"}, "author": "SilverStripe Ltd", "engines": {"node": "^18.x"}, "scripts": {"build": "yarn && yarn lint && yarn test && rm -rf client/dist/* && NODE_ENV=production webpack --mode production --bail --progress", "dev": "NODE_ENV=development webpack --progress", "watch": "NODE_ENV=development webpack --watch --progress", "css": "WEBPACK_CHILD=css npm run build", "test": "jest", "coverage": "jest --coverage", "lock": "npm-shrinkwrap --dev", "lint": "yarn lint-js && yarn lint-sass", "lint-js": "eslint client/src", "lint-js-fix": "eslint client/src --fix", "lint-sass": "stylelint client/src"}, "jest": {"testEnvironment": "jsdom", "roots": ["client/src"], "modulePaths": ["client/src", "../admin/client/src", "../admin/node_modules", "vendor/silverstripe/admin/client/src", "vendor/silverstripe/admin/node_modules"], "testMatch": ["**/tests/**/*-test.js?(x)"], "transform": {".*": "babel-jest"}}, "devDependencies": {"@silverstripe/eslint-config": "^1.3.0", "@silverstripe/webpack-config": "^2.0.0", "@storybook/addon-actions": "^6.5.13", "@storybook/addons": "^6.5.13", "@storybook/react": "^6.5.13", "@testing-library/react": "^14.0.0", "babel-jest": "^29.3.0", "jest-cli": "^29.3.0", "jest-environment-jsdom": "^29.3.1", "terser-webpack-plugin": "^5.3.6", "webpack": "^5.74.0", "webpack-cli": "^5.0.0"}, "dependencies": {"@apollo/client": "^3.7.1", "@popperjs/core": "^2.11.6", "bootstrap": "^4.6.2", "classnames": "^2.3.2", "core-js": "^3.26.0", "create-react-class": "^15.7.0", "graphql": "^16.8.1", "graphql-fragments": "^0.1.0", "graphql-tag": "^2.12.6", "griddle-react": "^0.8.2", "moment": "^2.29.4", "prop-types": "^15.8.1", "qs": "^6.11.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "react-router": "^6.4.3", "react-router-redux": "^4.0.8", "reactstrap": "^8.9.0", "redux": "^4.2.0", "url": "^0.11.0", "uuid": "^9.0.0"}, "resolutions": {"colors": "1.4.0"}, "browserslist": ["defaults"]}