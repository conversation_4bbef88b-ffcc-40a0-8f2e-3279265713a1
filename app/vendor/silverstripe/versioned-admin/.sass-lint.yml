# sass-lint config to match the AirBNB style guide
files:
  include: '**/client/src/**/*.scss'
  ignore:
    - 'client/src/styles/legacy/*'
    - 'admin/client/src/styles/legacy/*'
options:
  formatter: stylish
  merge-default-rules: false
rules:
  # Warnings
  # Things that require actual refactoring are marked as warnings
  class-name-format:
    - 1
    - convention: hyphenatedbem
  placeholder-name-format:
    - 1
    - convention: hyphenatedlowercase
  nesting-depth:
    - 1
    - max-depth: 3
  no-ids: 1
  no-important: 1
  no-misspelled-properties:
    - 1
    - extra-properties:
       - "-moz-border-radius-topleft"
       - "-moz-border-radius-topright"
       - "-moz-border-radius-bottomleft"
       - "-moz-border-radius-bottomright"
  variable-name-format:
    - 1
    - allow-leading-underscore: true
      convention: hyphenatedlowercase
  no-extends: 1

  # Warnings: these things are preferential rather than mandatory
  no-css-comments: 1

  # Errors
  # Things that can be easily fixed are marked as errors
  indentation:
    - 2
    - size: 2
  final-newline:
    - 2
    - include: true
  no-trailing-whitespace: 2
  border-zero:
    - 2
    - convention: '0'
  brace-style:
    - 2
    - allow-single-line: true
  clean-import-paths:
    - 2
    - filename-extension: false
      leading-underscore: false
  no-debug: 2
  no-empty-rulesets: 2
  no-invalid-hex: 2
  no-mergeable-selectors: 2
  # no-qualifying-elements:
  #   - 1
  #   - allow-element-with-attribute: false
  #     allow-element-with-class: false
  #     allow-element-with-id: false
  no-trailing-zero: 2
  no-url-protocols: 2
  quotes:
    - 2
    - style: double
  space-after-bang:
    - 2
    - include: false
  space-after-colon:
    - 2
    - include: true
  space-after-comma:
    - 2
    - include: true
  space-before-bang:
    - 2
    - include: true
  space-before-brace:
    - 2
    - include: true
  space-before-colon: 2
  space-between-parens:
    - 2
    - include: false
  trailing-semicolon: 2
  url-quotes: 2
  zero-unit: 2
  single-line-per-selector: 2
  one-declaration-per-line: 2
  empty-line-between-blocks:
    - 2
    - ignore-single-line-rulesets: true


  # Missing rules
  # There are no sass-lint rules for the following AirBNB style items, but thess
  # - Put comments on their own line
  # - Put property delcarations before mixins

  # Disabled rules

  # These are other rules that we may wish to consider using in the future
  # They are not part of the AirBNB CSS standard but they would introduce some strictness
  # bem-depth: 0
  # variable-for-property: 0
  # no-transition-all: 0
  # hex-length:
  #   - 1
  #   - style: short
  # hex-notation:
  #   - 1
  #   - style: lowercase
  # property-units:
  #   - 1
  #   - global:
  #       - ch
  #       - em
  #       - ex
  #       - rem
  #       - cm
  #       - in
  #       - mm
  #       - pc
  #       - pt
  #       - px
  #       - q
  #       - vh
  #       - vw
  #       - vmin
  #       - vmax
  #       - deg
  #       - grad
  #       - rad
  #       - turn
  #       - ms
  #       - s
  #       - Hz
  #       - kHz
  #       - dpi
  #       - dpcm
  #       - dppx
  #       - '%'
  #     per-property: {}
  # force-attribute-nesting: 1
  # force-element-nesting: 1
  # force-pseudo-nesting: 1
  # function-name-format:
  #   - 1
  #   - allow-leading-underscore: true
  #     convention: hyphenatedlowercase
  # no-color-literals: 1
  # no-duplicate-properties: 1
  # mixin-name-format:
  #   - 1
  #   - allow-leading-underscore: true
  #     convention: hyphenatedlowercase
  # shorthand-values:
  #   - 1
  #   - allowed-shorthands:
  #       - 1
  #       - 2
  #       - 3
  # leading-zero:
  #   - 1
  #   - include: false
  # no-vendor-prefixes:
  #   - 1
  #   - additional-identifiers: []
  #     excluded-identifiers: []
  #     placeholder-in-extend: 1
  # no-color-keywords: 2
