{"name": "silverstripe/assets", "description": "SilverStripe Assets component", "type": "silverstripe-vendormodule", "homepage": "http://silverstripe.org", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["silverstripe", "assets"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "require": {"php": "^8.1", "silverstripe/framework": "^5.1", "silverstripe/vendor-plugin": "^2", "symfony/filesystem": "^6.1", "intervention/image": "^2.7.2", "league/flysystem": "^3.9.0"}, "require-dev": {"silverstripe/recipe-testing": "^3", "silverstripe/versioned": "^2", "squizlabs/php_codesniffer": "^3.7", "mikey179/vfsstream": "^v1.6.11", "silverstripe/standards": "^1", "phpstan/extension-installer": "^1.3"}, "suggest": {"ext-exif": "If you use GD backend (the default) you may want to have EXIF extension installed to elude some tricky issues"}, "extra": {"installer-name": "silverstripe-assets"}, "autoload": {"psr-4": {"SilverStripe\\Assets\\": "src/", "SilverStripe\\Assets\\Tests\\": "tests/php/"}}, "scripts": {"lint": "phpcs src/ tests/php/", "lint-clean": "phpcbf src/ tests/php/"}, "minimum-stability": "dev", "prefer-stable": true}