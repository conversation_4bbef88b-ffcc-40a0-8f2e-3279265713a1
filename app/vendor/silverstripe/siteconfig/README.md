# SiteConfig

[![CI](https://github.com/silverstripe/silverstripe-siteconfig/actions/workflows/ci.yml/badge.svg)](https://github.com/silverstripe/silverstripe-siteconfig/actions/workflows/ci.yml)
[![Silverstripe supported module](https://img.shields.io/badge/silverstripe-supported-0071C4.svg)](https://www.silverstripe.org/software/addons/silverstripe-commercially-supported-module-list/)

## Introduction

SiteConfig provides a `Settings` tab in the admin section allowing users to set
site wide global configuration.

## Installation

```sh
composer require silverstripe/siteconfig
```

## Configuration

After installation, make sure you rebuild your database through `dev/build`.

## Development and Contribution

If you would like to make changes to the Silverstripe core codebase, we have an extensive [guide to contributing code](https://docs.silverstripe.org/en/4/contributing/code/).

## Links

 * [Requirements](https://docs.silverstripe.org/en/4/getting_started/server_requirements/)
 * [Changelogs](https://docs.silverstripe.org/framework/en/changelogs/)
 * [Forums](https://forum.silverstripe.org/)
 * [License](./LICENSE)
