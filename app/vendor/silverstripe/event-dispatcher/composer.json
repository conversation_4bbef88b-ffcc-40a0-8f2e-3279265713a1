{"name": "silverstripe/event-dispatcher", "description": "Publish and subscribe to events in Silverstripe CMS or your Silverstripe application", "type": "silverstripe-vendormodule", "keywords": ["silverstripe", "CMS", "events"], "license": "BSD-3-<PERSON><PERSON>", "require": {"php": "^8.1", "silverstripe/framework": "^5", "symfony/event-dispatcher": "^6.1", "psr/event-dispatcher": "^1"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "^3.7"}, "autoload": {"psr-4": {"SilverStripe\\EventDispatcher\\": "src/", "SilverStripe\\EventDispatcher\\Tests\\": "tests/"}}, "scripts": {"lint": "vendor/bin/phpcs src/ tests/", "lint-clean": "vendor/bin/phpcbf src/ tests/"}, "minimum-stability": "dev", "prefer-stable": true}