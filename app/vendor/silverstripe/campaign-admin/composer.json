{"name": "silverstripe/campaign-admin", "description": "SilverStripe campaign admin interface", "type": "silverstripe-vendormodule", "homepage": "http://silverstripe.org", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["silverstripe", "campaign", "versioned", "admin"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "require": {"php": "^8.1", "silverstripe/admin": "^2", "silverstripe/framework": "^5.4", "silverstripe/versioned": "^2", "silverstripe/vendor-plugin": "^2"}, "require-dev": {"phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7", "silverstripe/asset-admin": "^2", "silverstripe/cms": "^5", "silverstripe/standards": "^1", "phpstan/extension-installer": "^1.3"}, "extra": {"expose": ["client/dist", "client/lang"]}, "autoload": {"psr-4": {"SilverStripe\\CampaignAdmin\\": "src/", "SilverStripe\\CampaignAdmin\\Tests\\": "tests/php/", "SilverStripe\\CampaignAdmin\\Tests\\Behat\\Context\\": "tests/behat/src/"}}, "scripts": {"lint": "phpcs src/ tests/php/ tests/behat/src/"}, "minimum-stability": "dev", "prefer-stable": true}