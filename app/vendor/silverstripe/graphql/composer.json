{"name": "silverstripe/graphql", "description": "GraphQL server for SilverStripe models and other data", "type": "silverstripe-vendormodule", "license": "BSD-3-<PERSON><PERSON>", "require": {"php": "^8.1", "silverstripe/framework": "^5.4", "silverstripe/vendor-plugin": "^2", "webonyx/graphql-php": "^15.0.1", "silverstripe/event-dispatcher": "^1", "guzzlehttp/guzzle": "^7.5.0", "guzzlehttp/psr7": "^2.4.1", "m1/env": "^2.2.0"}, "require-dev": {"phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7", "silverstripe/asset-admin": "^2", "silverstripe/versioned-admin": "^2", "dnadesign/silverstripe-elemental": "^5", "silverstripe/frameworktest": "^1", "silverstripe/standards": "^1", "phpstan/extension-installer": "^1.3"}, "autoload": {"psr-4": {"SilverStripe\\GraphQL\\": "src/", "SilverStripe\\GraphQL\\Tests\\": "tests/"}}, "config": {"process-timeout": 600}, "scripts": {"lint": "vendor/bin/phpcs src/ tests/", "lint-clean": "vendor/bin/phpcbf src/ tests/"}, "minimum-stability": "dev", "prefer-stable": true, "suggest": {"silverstripe/graphql-devtools": "Adds in-browser tools for testing and debugging GraphQL queries", "silverstripe/assets": "Allows schema to be cached in the filesystem."}}