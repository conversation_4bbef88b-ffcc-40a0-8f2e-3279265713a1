# Silverstripe CMS GraphQL Server

[![CI](https://github.com/silverstripe/silverstripe-graphql/actions/workflows/ci.yml/badge.svg)](https://github.com/silverstripe/silverstripe-graphql/actions/workflows/ci.yml)
[![Silverstripe supported module](https://img.shields.io/badge/silverstripe-supported-0071C4.svg)](https://www.silverstripe.org/software/addons/silverstripe-commercially-supported-module-list/)

This modules serves Silverstripe data as
[GraphQL](http://facebook.github.io/react/blog/2015/05/01/graphql-introduction.html)
representations, with helpers to generate schemas based on Silverstripe model
introspection. It layers a pluggable schema registration system on top of the
[graphql-php](https://github.com/webonyx/graphql-php) library. The APIs are
very similar.

## Installation

```sh
composer require silverstripe/graphql
```

## Documentation

See [docs.silverstripe.org](https://docs.silverstripe.org/en/developer_guides/graphql/).
