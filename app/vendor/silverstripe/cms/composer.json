{"name": "silverstripe/cms", "type": "silverstripe-vendormodule", "description": "The SilverStripe Content Management System", "homepage": "http://silverstripe.org", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["silverstripe", "cms"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "require": {"php": "^8.1", "silverstripe/admin": "^2.2", "silverstripe/campaign-admin": "^2", "silverstripe/framework": "^5.4", "silverstripe/reports": "^5", "silverstripe/siteconfig": "^5", "silverstripe/versioned": "^2", "silverstripe/versioned-admin": "^2", "silverstripe/vendor-plugin": "^2"}, "require-dev": {"phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7", "silverstripe/standards": "^1", "phpstan/extension-installer": "^1.3"}, "extra": {"expose": ["client/dist", "client/lang"]}, "scripts": {"lint": "phpcs -s code/ tests/php/ tests/behat/src/", "lint-clean": "phpcbf code/ tests/php/ tests/behat/src/"}, "autoload": {"psr-4": {"SilverStripe\\CMS\\": ["code/", "_legacy/"], "SilverStripe\\CMS\\Tests\\": "code/php/", "SilverStripe\\CMS\\Tests\\Behaviour\\": "tests/behat/src/"}}, "prefer-stable": true, "minimum-stability": "dev"}