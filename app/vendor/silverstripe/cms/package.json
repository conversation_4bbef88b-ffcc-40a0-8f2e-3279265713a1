{"name": "silverstripe-cms", "version": "5.0.0", "description": "The SilverStripe CMS", "directories": {"test": "tests"}, "engines": {"node": "^18.x"}, "scripts": {"build": "yarn && yarn lint && yarn test && rm -rf client/dist/* && NODE_ENV=production webpack --mode production --bail --progress", "dev": "NODE_ENV=development webpack --progress", "watch": "NODE_ENV=development webpack --watch --progress", "css": "WEBPACK_CHILD=css npm run build", "test": "jest", "coverage": "jest --coverage", "lint": "eslint client/src && stylelint client/src"}, "repository": {"type": "git", "url": "git://github.com/silverstripe/silverstripe-cms.git"}, "keywords": ["silverstripe", "cms"], "author": "SilverStripe Ltd", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/silverstripe/silverstripe-cms/issues"}, "homepage": "https://github.com/silverstripe/silverstripe-cms#readme", "dependencies": {"@apollo/client": "^3.7.1", "@popperjs/core": "^2.11.6", "bootstrap": "^4.6.2", "classnames": "^2.3.2", "deep-freeze-strict": "^1.1.1", "graphql": "^16.8.1", "graphql-tag": "^2.12.6", "isomorphic-fetch": "^3.0.0", "merge": "^2.1.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "react-select": "^5.6.0", "reactstrap": "^8.9.0", "reactstrap-confirm": "^1.3.2", "redux": "^4.2.0", "redux-form": "^8.3.8"}, "devDependencies": {"@silverstripe/eslint-config": "^1.3.0", "@silverstripe/webpack-config": "^2.1.0", "@testing-library/react": "^14.0.0", "babel-jest": "^29.3.0", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.26.0", "jest-cli": "^29.3.0", "jest-environment-jsdom": "^29.3.1", "webpack": "^5.74.0", "webpack-cli": "^5.0.0"}, "browserslist": ["defaults"], "jest": {"testEnvironment": "jsdom", "roots": ["client/src"], "moduleDirectories": ["client/src", "node_modules", "../admin/client/src", "../admin", "vendor/silverstripe/admin/client/src", "vendor/silverstripe/admin", "node_modules/@silverstripe/webpack-config/node_modules"], "testMatch": ["**/tests/**/*-test.js?(x)"], "transform": {".*": "babel-jest"}}, "resolutions": {"colors": "1.4.0"}}