{"name": "silverstripe/admin", "description": "SilverStripe admin interface", "type": "silverstripe-vendormodule", "homepage": "http://silverstripe.org", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["silverstripe", "admin"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "require": {"php": "^8.1", "silverstripe/framework": "^5.4", "silverstripe/versioned": "^2", "silverstripe/vendor-plugin": "^2"}, "require-dev": {"phpunit/phpunit": "^9.6", "silverstripe/frameworktest": "^1", "squizlabs/php_codesniffer": "^3.7", "silverstripe/standards": "^1", "phpstan/extension-installer": "^1.3"}, "conflict": {"dnadesign/silverstripe-elemental": "<5.3.0"}, "extra": {"expose": ["client/dist", "client/lang", "thirdparty"]}, "autoload": {"psr-4": {"SilverStripe\\Admin\\": "code/", "SilverStripe\\Admin\\Tests\\": "tests/php/", "SilverStripe\\Admin\\Tests\\Behat\\Context\\": "tests/behat/src/"}}, "scripts": {"lint": "phpcs code/ tests/php/", "lint-clean": "phpcbf code/ tests/php/"}, "minimum-stability": "dev", "prefer-stable": true}