{"name": "<PERSON><PERSON><PERSON>e-session-manager", "description": "Allow users to manage and revoke access to multiple login sessions across devices.", "main": "./client/src/boot/index.js", "author": "SilverStripe Ltd", "engines": {"node": ">=18.x"}, "scripts": {"build": "yarn && yarn lint && yarn test && rm -rf client/dist/* && NODE_ENV=production webpack --mode production --bail --progress", "dev": "NODE_ENV=development webpack --progress", "watch": "NODE_ENV=development webpack --watch --progress", "css": "WEBPACK_CHILD=css npm run build", "test": "jest", "coverage": "jest --coverage", "lint": "yarn lint-js && yarn lint-sass", "lint-js": "eslint client/src", "lint-js-fix": "eslint client/src --fix", "lint-sass": "stylelint client/src"}, "dependencies": {"babel-polyfill": "^6.26.0", "core-js": "^3.26.0", "moment": "^2.29.4", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "reactstrap": "^8.9.0", "reactstrap-confirm": "^1.3.2", "redux": "^4.2.0"}, "devDependencies": {"@babel/runtime": "^7.20.1", "@silverstripe/eslint-config": "^1.3.0", "@silverstripe/webpack-config": "^2.1.0", "@testing-library/react": "^14.0.0", "babel-jest": "^29.3.0", "babel-plugin-transform-runtime": "^6.23.0", "jest-cli": "^29.3.0", "jest-environment-jsdom": "^29.3.1", "mockdate": "^3.0.5", "react-test-renderer": "^18.2.0", "webpack": "^5.74.0", "webpack-cli": "^5.0.0"}, "resolutions": {"colors": "1.4.0"}, "browserslist": ["defaults"], "jest": {"testEnvironment": "jsdom", "roots": ["client/src"], "moduleDirectories": ["client/src", "node_modules", "../admin/client/src", "../admin/node_modules", "../silverstripe/admin/client/src", "../silverstripe/admin/node_modules", "../../silverstripe/admin/client/src", "../../silverstripe/admin/node_modules", "vendor/silverstripe/admin/client/src", "vendor/silverstripe/admin/node_modules"], "collectCoverageFrom": ["**/*.{js,jsx}", "!**/node_modules/**", "!**/boot/**", "!**/bundles/**", "!**/legacy/**", "!**/vendor/**", "!**/*-test.{js,jsx}", "!**/*-story.{js,jsx}"], "globalSetup": "./jest-global-setup.js", "testMatch": ["**/tests/**/*-test.js?(x)"], "transform": {".*": "babel-jest"}}}