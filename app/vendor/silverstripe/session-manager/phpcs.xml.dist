<?xml version="1.0" encoding="UTF-8"?>
<ruleset name="SilverStripe">
    <description>CodeSniffer ruleset for SilverStripe coding conventions.</description>

    <file>src</file>
    <file>tests</file>

    <!-- base rules are PSR-12 -->
    <rule ref="PSR12" >
        <exclude name="PSR1.Methods.CamelCapsMethodName.NotCamelCaps" />
		<exclude name="PSR1.Files.SideEffects.FoundWithSymbols" />
    </rule>
</ruleset>
