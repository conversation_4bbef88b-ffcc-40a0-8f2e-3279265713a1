# Silverstripe CMS Session Manager

[![CI](https://github.com/silverstripe/silverstripe-session-manager/actions/workflows/ci.yml/badge.svg)](https://github.com/silverstripe/silverstripe-session-manager/actions/workflows/ci.yml)
[![Silverstripe supported module](https://img.shields.io/badge/silverstripe-supported-0071C4.svg)](https://www.silverstripe.org/software/addons/silverstripe-commercially-supported-module-list/)

Allow members to manage and revoke access to multiple login sessions across devices.

## Installation

```sh
composer require silverstripe/session-manager
```

## Documentation

See [Managing Sessions](https://docs.silverstripe.org/en/developer_guides/cookies_and_sessions/managing_sessions/) on [docs.silverstripe.org](https://docs.silverstripe.org).
