{"name": "silverstripe/session-manager", "description": "Allow users to manage and revoke access to multiple login sessions across devices.", "type": "silverstripe-vendormodule", "license": "BSD-3-<PERSON><PERSON>", "homepage": "https://github.com/silverstripe/silverstripe-session-manager", "keywords": ["silverstripe", "session"], "authors": [{"name": "<PERSON>z <PERSON>", "homepage": "https://github.com/kinglozzer"}, {"name": "Silverstripe Ltd.", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "require": {"php": "^8.1", "silverstripe/admin": "^2", "silverstripe/framework": "^5.3", "symfony/http-foundation": "^6.1", "ua-parser/uap-php": "^3.9.14"}, "require-dev": {"silverstripe/recipe-testing": "^3", "symbiote/silverstripe-queuedjobs": "^5", "silverstripe/standards": "^1", "phpstan/extension-installer": "^1.3"}, "suggest": {"symbiote/silverstripe-queuedjobs": "^5", "silverstripe/auditor": "^3"}, "autoload": {"psr-4": {"SilverStripe\\SessionManager\\": "src/", "SilverStripe\\SessionManager\\Tests\\": "tests/php/", "SilverStripe\\SessionManager\\Tests\\Behat\\Context\\": "tests/behat/src/"}}, "extra": {"expose": ["client/dist"]}, "minimum-stability": "dev", "prefer-stable": true}