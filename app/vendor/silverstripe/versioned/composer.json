{"name": "silverstripe/versioned", "description": "SilverStripe Versioned component", "type": "silverstripe-vendormodule", "homepage": "http://silverstripe.org", "license": "BSD-3-<PERSON><PERSON>", "keywords": ["silverstripe", "versioned"], "authors": [{"name": "SilverStripe", "homepage": "http://silverstripe.com"}, {"name": "The SilverStripe Community", "homepage": "http://silverstripe.org"}], "require": {"php": "^8.1", "silverstripe/framework": "^5.4", "symfony/cache": "^6.1", "silverstripe/vendor-plugin": "^2"}, "require-dev": {"silverstripe/recipe-testing": "^3", "silverstripe/graphql": "^5", "squizlabs/php_codesniffer": "^3.7", "silverstripe/standards": "^1", "phpstan/extension-installer": "^1.3"}, "autoload": {"psr-4": {"SilverStripe\\Versioned\\": "src/", "SilverStripe\\Versioned\\Tests\\": "tests/php/"}}, "scripts": {"lint": "vendor/bin/phpcs src/ tests/php/", "lint-clean": "vendor/bin/phpcbf src/ tests/php/"}, "minimum-stability": "dev", "prefer-stable": true}