## Silverstripe CMS Recipe

[![CI](https://github.com/silverstripe/recipe-cms/actions/workflows/ci.yml/badge.svg)](https://github.com/silverstripe/recipe-cms/actions/workflows/ci.yml)
[![Silverstripe supported module](https://img.shields.io/badge/silverstripe-supported-0071C4.svg)](https://www.silverstripe.org/software/addons/silverstripe-commercially-supported-module-list/)

Base page and asset content-editing recipe for a Silverstripe ([http://silverstripe.org](http://silverstripe.org))
installation. This includes the modules:

Provided by [silverstripe/recipe-core]:

 * [framework](http://github.com/silverstripe/silverstripe-framework): Module including the base framework
 * [config](https://github.com/silverstripe/silverstripe-config): Core config library
 * [assets](http://github.com/silverstripe/silverstripe-assets): Filesystem module

Provided by [silverstripe/recipe-cms]:

 * [admin](http://github.com/silverstripe/silverstripe-admin)
 * [asset-admin](http://github.com/silverstripe/silverstripe-asset-admin)
 * [campaign-admin](http://github.com/silverstripe/silverstripe-campaign-admin)
 * [versioned-admin](http://github.com/silverstripe/silverstripe-versioned-admin)
 * [cms](http://github.com/silverstripe/silverstripe-cms)
 * [errorpage](http://github.com/silverstripe/silverstripe-errorpage)
 * [reports](http://github.com/silverstripe/silverstripe-reports)
 * [graphql](http://github.com/silverstripe/silverstripe-graphql)
 * [session-manager](http://github.com/silverstripe/silverstripe-session-manager)
 * [siteconfig](http://github.com/silverstripe/silverstripe-siteconfig)
 * [versioned](http://github.com/silverstripe/silverstripe-versioned)

This can be either added to an existing project or used as a project base for creating a
fully featured Silverstripe CMS project.

See the [recipe plugin](https://github.com/silverstripe/recipe-plugin) page for instructions on how
Silverstripe recipes work.
