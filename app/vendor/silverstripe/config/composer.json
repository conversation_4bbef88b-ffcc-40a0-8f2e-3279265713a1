{"name": "silverstripe/config", "description": "SilverStripe configuration based on YAML and class statics", "license": "BSD-3-<PERSON><PERSON>", "require": {"php": "^8.1", "symfony/finder": "^6.1", "symfony/yaml": "^6.1", "marcj/topsort": "^2.0.0", "psr/simple-cache": "^3.0.0"}, "require-dev": {"phpunit/phpunit": "^9.6", "mikey179/vfsstream": "^1.6", "squizlabs/php_codesniffer": "^3.7", "silverstripe/standards": "^1", "phpstan/extension-installer": "^1.3"}, "autoload": {"psr-4": {"SilverStripe\\Config\\": "src/", "SilverStripe\\Config\\Tests\\": "tests/"}}}