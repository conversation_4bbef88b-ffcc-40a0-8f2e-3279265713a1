# Config

[![CI](https://github.com/silverstripe/silverstripe-config/actions/workflows/ci.yml/badge.svg)](https://github.com/silverstripe/silverstripe-config/actions/workflows/ci.yml)
[![Silverstripe supported module](https://img.shields.io/badge/silverstripe-supported-0071C4.svg)](https://www.silverstripe.org/software/addons/silverstripe-commercially-supported-module-list/)

The aim of this is to implement the Silverstripe config system in a way that can be
understood by many and to improve performance whilst keeping the powerful features.

## Installation

```sh
composer require silverstripe/config
```

# Documentation

* [Manifesto](docs/manifesto.md)
* [Architecture](docs/architecture.md)
* [Transformers](docs/transformers.md)
* [Middleware](docs/middleware.md)
* [Usage](docs/usage.md)
* [Testing](docs/testing.md)
* [Glossary](docs/glossary.md)

