{"name": "m1/env", "description": "Env is a lightweight library bringing .env file parser compatibility to PHP. In short - it enables you to read .env files with PHP.", "keywords": ["m1", "env", "loader", "config", ".env", "dotenv", "support", "parser"], "homepage": "https://github.com/m1/Env", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://milescroxford.com", "role": "Developer"}], "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.*", "scrutinizer/ocular": "~1.1", "squizlabs/php_codesniffer": "^2.3"}, "suggest": {"josegonzalez/dotenv": "For loading of .env", "m1/vars": "For loading of configs"}, "autoload": {"psr-4": {"M1\\Env\\": "src"}}, "autoload-dev": {"psr-4": {"M1\\Env\\Test\\": "tests"}}, "scripts": {"test": "phpunit", "check-codestyle": "phpcs -p --standard=PSR2 --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src tests", "fix-codestyle": "phpcbf -p --standard=PSR2 --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1 src tests"}}