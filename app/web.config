<!-- Routing configuration for Microsoft IIS web server -->
<configuration>
    <system.webServer>
        <rewrite>
            <rules>
                <rule name="SilverStripe Global URLs">
                    <match url="^(.*)$" />
                    <conditions>
                        <add input="{R:1}" matchType="Pattern" pattern="public(/|$)" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="public/{R:1}" appendQueryString="true" />
                </rule>
                <rule name="SilverStripe Preprocessed URLs" stopProcessing="true">
                    <match url="^(.*)$" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="public/index.php" appendQueryString="true" />
                </rule>
            </rules>
        </rewrite>
    </system.webServer>
</configuration>
