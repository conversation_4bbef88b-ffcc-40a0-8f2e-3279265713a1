name: Merge-up

on:
  # At 10:50 PM UTC, only on Friday
  schedule:
    - cron: '50 22 * * 5'
  workflow_dispatch:

jobs:
  merge-up:
    name: Merge-up
    # Only run cron on the silverstripe-themes account
    if: (github.event_name == 'schedule' && github.repository_owner == 'silverstripe-themes') || (github.event_name != 'schedule')
    runs-on: ubuntu-latest
    steps:
      - name: Merge-up
        uses: silverstripe/gha-merge-up@v1
