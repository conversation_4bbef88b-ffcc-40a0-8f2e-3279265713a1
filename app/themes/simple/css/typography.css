/** ----------------------------------------------------------
 *
 * Contains the generic theme typography styles
 *
 *    Include your notes or table of contents here
 *
 *    Include color hex's or values of your grid
 *      #333333             - body
 *      #B80000             - links, headings
 *      #D80000             - links on hover
 *      5px Vertical grid   - based on multiples of 5px, eg 10px, 15px, 20px, 25px...
 *
 *    <AUTHOR> Name <<EMAIL>>
 *
 * ------------------------------------------------------- */

/* TEMPLATE CUSTOM FONTS */
  /* Generated by Font Squirrel (http://www.fontsquirrel.com) on February 20, 2012 06:04:25 AM America/New_York */

  /* add your own custom fonts here */

  @font-face {
      font-family: 'CamboRegular';
      src: url('../webfonts/Cambo-Regular-webfont.eot');
      src: url('../webfonts/Cambo-Regular-webfont.eot?#iefix') format('embedded-opentype'), url('../webfonts/Cambo-Regular-webfont.woff') format('woff'), url('../webfonts/Cambo-Regular-webfont.ttf') format('truetype'), url('../webfonts/Cambo-Regular-webfont.svg#CamboRegular') format('svg');
      font-weight: normal;
      font-style: normal;
  }
  @font-face {
      font-family: 'WebSymbolsRegular';
      src: url('../webfonts/websymbols-regular-webfont.eot');
      src: url('../webfonts/websymbols-regular-webfont.eot?#iefix') format('embedded-opentype'), url('../webfonts/websymbols-regular-webfont.woff') format('woff'), url('../webfonts/websymbols-regular-webfont.ttf') format('truetype'), url('../webfonts/websymbols-regular-webfont.svg#WebSymbolsRegular') format('svg');
      font-weight: normal;
      font-style: normal;
  }


/* BASE TYPOGRPHY */

/* These are the default styles for the Simple theme */
body {
    font-size: 13px; /* This overrides the browsers default font size */
    line-height: 20px; /* If you change the font-size make sure you change the line-height value as well - the usual ratio is around 1.5 (font-size x 1.5 = line-height) */
    margin-bottom: 20px;
    color: #333333;
    font-family: Arial, Helvetica, sans-serif;
}
  body a { text-decoration: none; } /* this removes the underline from all links */
  body a:link { -webkit-tap-highlight-color: #b80000 } /* this sets the highlight color when links are tapped on Safari (browser) on iPhone */


/* HEADERS */
.typography h1,
.typography h2,
.typography h3,
.typography h4,
.typography h5,
.typography h6 {
    font-family: "CamboRegular", Georgia, "Times New Roman", Times, serif; /* This references one of the custom @font-face fonts - the other fonts that are referenced are fallbacks for browsers that don't support @fontface */
    font-weight: normal;
    margin-bottom: 10px;
    color: #444;
}
.typography h1 {
  font-size: 36px;
  line-height: 45px;
  margin: 0 0 25px 0;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 5px;
}
.typography h2 { font-size: 28px; line-height: 35px; margin-bottom: 15px; }
.typography h3 { font-size: 22px; line-height: 30px; margin-bottom: 10px; }
.typography h4 { font-size: 18px; line-height: 25px; margin-bottom: 5px; }
.typography h5 { font-size: 16px; line-height: 20px; margin-bottom: 5px; }
.typography h6 { font-size: 14px; line-height: 20px; font-weight: bold; margin-bottom: 5px; }


/* PARAGRAGHS */
.typography  p { font-size: 13px; line-height: 20px; margin: 0 0 20px; }
  .typography .intro {
      font-family: "CamboRegular", Georgia, "Times New Roman", Times, serif;
      font-size: 22px;
      line-height: 32px;
      margin-bottom: 20px;
  }
.typography em { font-style: italic; }

::selection,
::-moz-selection { /* Applies style to highlighted portion of a page */
    background: #b80000;
    color: #fff;
    text-shadow: none;
}

/* LINKS */
.typography a,
.typography a.intro {
    color: #B80000;
    text-decoration: none;
}
  .typography a:hover {
    color: #D80000;
    border-bottom: 1px dashed #B80000;
  }
  .typography a:focus { }


/* LIST STYLES
-------------------------------------------- */
.typography ul,
.typography ol,
.typography dl { margin: 0 0 20px 25px; }
.typography ul li { list-style-type: disc; } /* adds disc style bullet to the list */
  .typography li { margin-bottom: 5px; }


/* TABLE STYLES
-------------------------------------------- */
.typography table {
    border-collapse: collapse; /* borders are collapsed into a single border when possible */
    border: 1px solid #d4d4d4;
    border-spacing: 0; /* The border-spacing property sets the distance between the borders of adjacent cells - acts as a backup to border-collapse: collapse */
    margin: 0 0 10px;
    text-align: left;
}
  .typography table tr:nth-child(even) {
      background-color: #ededed
  }
  .typography table tr.even,
  .typography table th,
  .typography thead td {
      background-color: #ededed
  }
  .typography table td,
  .typography table th {
      padding: 2px 5px;
      border: 1px solid #d4d4d4;
      vertical-align: top;
  }
  .typography table th {
      font-weight: bold;
  }


/* WYSIWYG EDITOR ALIGNMENT CLASSES
-------------------------------------------- */
.typography .text-left, .typography .left {
    text-align: left
}
.typography .text-center, .typography .center {
    text-align: center
}
.typography .text-right, .typography .right {
    text-align: right
}


/* IMAGES
-------------------------------------------- */
.typography img {
    border: 5px solid #d7d7d7;
    height: auto; /* resets the image height so that it maintains its aspect ratio when width is set */
    background: transparent url(../images/ajax-loader.gif) no-repeat center center;
}
.typography img.left {
    float: left;
    max-width: 50%;
    margin: 5px 20px 10px 0;
}
.typography img.right {
    float: right;
    max-width: 50%; /* Responsive width */
    margin: 5px 0 10px 20px;
}
.typography img.leftAlone {
    float: left;
    margin-right: 100%;
    margin-bottom: 10px;
    clear: both;
}
.typography img.rightAlone {
    float: right;
    margin-left: 100%;
    margin-bottom: 10px;
    clear: both;
}
.typography img.center {
    float: none;
    margin-left: auto;
    margin-right: auto;
    display: block;
    margin-bottom: 10px;
    clear: both;
}
.typography .captionImage { width: 100%; margin-top: 5px; }
.typography .captionImage img { margin: 0; }
  .typography .captionImage.left {
    float: left;
    margin: 5px 30px 20px 0px;
  }
  .typography .captionImage.right{
    float: right;
    margin: 5px 0 20px 30px;
  }
  .typography .captionImage.left[style],
  .typography .captionImage.right[style] {
    max-width: 50%; /* Overides core width to make responsive */
  }
  .typography .captionImage.left img,
  .typography .captionImage.right img {
    float: none;
    max-width: none;
    width: 100%;
  }
  .typography .captionImage.left img {
    margin-right: -10px;
  }
  .typography .captionImage.right img {
    margin-left: -10px;
  }

.typography .captionImage.leftAlone {
    float: left;
    clear: both;
    margin-right: 100%;
}
.typography .captionImage.rightAlone {
    float: right;
    clear: both;
    margin-left: 100%;
}
.typography .captionImage.center{
  margin: 0 auto 20px;
}
.typography .captionImage p {
  clear: both;
  margin: 5px 0;
  font-style: italic;
  color: #888;
}
.typography .captionImage p.caption.text-center {
    text-align: center;
}
.typography .captionImage p.caption.text-left {
    text-align: left;
}


/* BLOCKQUOTES
-------------------------------------------- */
.typography blockquote {
  background: transparent url(../images/blockquote.png) no-repeat 0px 6px;
  font-family: "CamboRegular", Georgia, "Times New Roman", Times, serif;
  color: #777;
  display: block;
  font-style: italic;
  margin: 0 0 20px;
  float: right;
  text-indent: 30px;
  width: 50%;
  margin-left: 5%;
  clear: both;
}
.typography blockquote p {
  font-size: 17px;
  line-height: 25px;
}
.typography pre {
  background: #F7F7F7;
  border: 1px solid #E4E4E4;
  font-family: Courier, monospace;
  margin: 0 0 20px 0;
  padding: 15px;
  clear: both;
}

/* ADDRESS
-------------------------------------------- */
address {
  display: block;
  margin-bottom: 20px;
}
