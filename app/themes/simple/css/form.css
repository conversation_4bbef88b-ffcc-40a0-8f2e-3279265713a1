/** ----------------------------------------------------------
 *
 * This stylesheet includes both generic form styles and
 *    additional form styles for the User Defined Form Module.
 *
 ** ------------------------------------------------------- */


/* GENERIC FORMS
----------------------------------------------- */

form {
    max-width: 500px;
    width: 50%;
}
div.field {
    margin: 10px 0 15px;
}
fieldset {
    border: 0;
    margin: 0;
    padding: 0;
}
form label {
    margin-bottom: 5px;
}
    form label.left {
        display: block;
        font-weight: bold;
    }
    form label.right {
        font-size: 12px;
        color: #888;
    }

form input.text,
form textarea,
form .textajaxuniquetext,   /* Not sure if this is used? */
form select {
    width: 100%;
    max-width: 400px;
    padding: 6px 5px;
    font-size: 15px;
    background-color: #fff;
    border: 1px solid #dad9d9;
}
    .ie7 form select { width: 400px; } /* fix for ie7's rendering of max-width property on select input */

form input.text:focus,
form textarea:focus,
form .textajaxuniquetext:focus,
form select:focus {
    outline:none;
}
form input[disabled], form textarea[disabled] {
    background-color: #f7f7f7;
    border: 1px solid #dad9d9;
}
textarea {
    resize: vertical; /* forces text area to be resized vertically only */
}


/* Radio and Checkbox */
.field .checkbox:not(.field),
.field .radio:not(.field) {
    float: left;
    width: 13px;
    height: 13px;
    margin-right: 6px;
    margin-top: 5px;
    padding: 0;
}
    .checkbox label.right,
    .radio label.right {
        float: left;
    }


/* Messages */
form .message {
    background-color: #fef4ba;
    padding: 5px 10px;
    border: 1px solid #e8c805;
    border-radius: 3px;
}
    form .good {
        background-color: #ecf9d0;
        border-color: #8fbe00;
    }
    form .bad,
    form .required,
    form .error {
        background-color: #f9d0d0;
        border-color: #cf0000;
        color: #b80000;
    }


/* ACTIONS */
.Actions {
    margin-bottom: 20px;
}
    a.btn, button, input[type="submit"], input[type="reset"], .Actions .action {
        background: #b80000;
        color: #fff;
        display: inline-block;
        border-radius: 4px;
        padding: 6px 15px;
        margin-top: 10px;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 1px;
        border: none;
        font-size: 13px;
        margin-right: 10px;
        float: left;
    }
    a.btn:hover, button, input[type="submit"]:hover, .Actions .action:hover {
        background: #C80000;
        color: #fff;
    }
    a.btn {
        line-height: 18px;
        margin-bottom: 10px;
    }
    a.btn:after {                     /* creates arrow in button */
        content: '\2192';
        padding-left: 10px;
    }
    .ie7 input.action {
        width: 0;                     /* IE table-cell margin fix */
        overflow: visible;
    }
    input.action[class] {             /* IE ignores [class] */
        width: auto;                  /* cancel margin fix for other browsers */
    }

    .ie7 .Actions .action {
        float: left;
    }
    .Actions:after {
        color: #B94A48;
        display: inline-block;
        font-weight: normal;
        margin-top: 9px;
    }
    #MemberLoginForm_LoginForm .Actions:after {
        display: none;
    }


/* AREA SPECIFIC */
    /* LOGIN and FORGOT PASSWORD */
    #Remember {
        min-height: 20px;
    }
    #ForgotPassword {
        clear: left;
    }
    #MemberLoginForm_LostPasswordForm .Actions:after {
        display: none;
    }

    /* Search / Login */
    .header form .middleColumn {
        /* float: none;
        width: 100% !important; */
    }




/* USER DEFINED USER FORM MODULE STYLES
----------------------------------------------- */

/*Generic and mixed*/
.FormHeading {
    clear: both;
    padding-top: 15px;
}
form .date .middleColumn input {
    /*background: transparent url(../images/icons/your_icon_here.png) no-repeat scroll 90px 5px;*/
    width: 114px;
}
.Actions input.resetformaction,
.Actions input.action-minor {                  /* Clear button */
    float: left;
    background-color: #888;
}
.Actions input.resetformaction:hover,
.Actions input.action-minor:hover {            /* Clear button */
    background-color: #aaa;
}

/* Labels */
.checkbox label.right {
    font-size: 13px;                            /* reset to default */
    color: #333;                                /* reset to default */
}
form .requiredField label.left:after, form .requiredField legend.left:after { /* pseudo element adds an asterisk to a required fields label */
    color: #B94A48;
    content: "*";
    font-size: 14px;
    font-weight: normal;
    padding-left: 3px;
}

/* Radio / Checkbox */
form .checkboxset ul,
form .optionset ul {
    margin: 0;
}
    form .checkboxset li,
    form .optionset li {
        margin-bottom: 5px;
        list-style-type:none;
    }
    form div.checkbox:after { /* clearfix */
        content: ".";
        display: block;
        height: 0;
        clear: both;
        visibility: hidden;
    }
    form .checkbox .checkbox { clear: both; }

/* Messages */
span.message {
    margin: 10px 0;
    display: block;
    max-width: 390px;
    clear: left;
}
div.holder-required {               /* This class needs to be changed - is used for both input and div */
    /* background-color: #f9d0d0;
    border: 1px solid #cf0000;
    padding: 10px;
    margin-left: -11px; */
}
form input.holder-required {        /* This class needs to be changed - is used for both input and div */
    border: 1px solid #cf0000;
}

/* Error messages */
input:invalid,
textarea:invalid {
    border-radius: 1px;
    box-shadow: 0px 0px 5px red;
}
.no-boxshadow input:invalid,
.no-boxshadow textarea:invalid {
    background-color: #f0dddd
}

/* To test - potentially not needed? */
.ss-uploadfield-item-info button{
    margin-top: 4px !important;
    float: left;
}
form .creditCardField input,
form input.currency,
form input.numeric {
    width: 50px;
}
form #DMYDate-month,
form #DMYDate-day {
    width: 25px
}




/* Responsive form styles
----------------------------------------------- */

@media only screen and (max-width: 700px) {

    /* To test - potentially not needed? */
	.header form .middleColumn {
	    float: none;
	    width: 100% !important;
	}
	form label.left,
	#MemberLoginForm_LoginForm label {
	    margin-bottom: 8px
	}
}

@media only screen and (max-width: 900px) {
    form {
        max-width: 100%;
    }
}

@media only screen and (min-width: 700px) {

}
