/** ----------------------------------------------------------
 *
 * Contains the main layout of the page and the individual styles.
 * Acts as the main stylesheet for theme.
 *
 *		Include your notes or table of contents below....
 *		Include color hex's or values of your grid
 *
 *		1. OOCSS GRID
 *		2. MAIN LAYOUT
 *		3. HEADER
 *			- Brand
 *			- Search Form
 *		4. Navigation
 *			- Primary Navigation
 *			- tablet Navigation
 *			- Secondary Navigation
 *			- Secondary Nav 2-5 Levels deep
 *		5. Mixed
 *		6. Footer
 *		7. Page Specific Layout
 *			- Homepage
 *			- Search Results
 *		8. Device and Responsive Layout
 *			- Breakpoint 960px
 *			- Breakpoint 640px
 *				- Search Form
 *				- Main Content
 *		9. Print Styles
 *			- Simple Theme custom print styles
 *
 * <AUTHOR> Name <<EMAIL>>
 * ------------------------------------------------------- */

/* OOCSS Grid
* https://github.com/stubbornella/oocss/wiki/grids
*/

.line, /* line - Groups units on one horizontal line. Note: for mobile layout units may be stacked to avoid horizontal scrolling. */
.lastUnit {
	overflow:hidden;
	*overflow:visible;
	*zoom:1;
	padding:0 10px;
}
.unit { /* unit - Base class which divides a line into sections (columns). */
	float:left;
	padding:0 10px;
	-moz-box-sizing:border-box;
	-webkit-box-sizing:border-box;
	box-sizing:border-box; /* box-sizing:border-box; creates a box-model where
	padding and border are NOT added onto the width - they are included in the width,
	so a 200px wide element with 20px padding will be 200px, NOT 240px wide */
}
.unitRightv { /* Use this class if you want to offset a column eg: |--content(.unit)--|--content(.unit)--|--no-content--|--no-content--|--content(.unitRighttv)--| */
	float:right;
}

/* sizeXofY - Extends unit. Indicates the fractional width of the unit, for example size3of4 would take up three quarters, or 75%, of the horizontal space.
The following fractions are supported: 1, 1/2, 1/3, 2/3, 1/4, 3/4, 1/5, 2/5, 3/5, 4/5 */
/* It is possible to add more columns if you wish you will just have to add the fractions that are missing eg: .size1of6 {width:16.66666%;} */

.size1of1 {
	float:none;
}
.size1of2 {
	width:50%;
}
.size1of3 {
	width:33.33333%;
}
.size2of3 {
	width:66.66666%;
}
.size1of4 {
	width:25%;
}
.size3of4 {
	width:75%;
}
.size1of5 {
	width:20%;
}
.size2of5 {
	width:40%;
}
.size3of5 {
	width:60%;
}
.size4of5 {
	width:80%;
}
.lastUnit { /* lastUnit - Extends unit. Applied to the last child of every line. */
	float:none;
	width:auto;
	_position:relative; /* Bug fix for IE6 - Internet Explorer 6 and below wouldn't fail on properties that were prefixed with non-alphanumeric characters.
	meaning that anything prefixed with _ wouldn't be picked up by any other browsers */
	_left:-3px;
	_margin-right:-3px;
}

/* MAIN LAYOUT */
body {
    margin: 0;
    background: #ededed;
    min-width: 240px;
    -webkit-text-size-adjust: none; /* The text size is not adjusted for Safari on iPhone */
}
	.ie7 body,
	.ie8 body {
	    min-width: 860px; /* media queries are not supported in ie7/8 without a polyfill */
	}
	.main {
	    background: #fff;
	    padding: 40px 0 60px;
	    min-height: 300px;
	}
	.inner {
	    max-width: 1100px;
	    margin: 0 auto;
	    padding: 0 30px;
	}
	.ie6 .inner {
	    width: 960px;
	}
	.no-sidebar .content-container {
	    float: left;
	    width:100%; /* makes content container full width when there is no sidebar */
	}
	.sidebar { /* this is the sidebar element */
	    margin-top: 12px;
	}
	.no-sidebar .sidebar {
		display: none;
	}


/* HEADER */
.header {
    background: #161616;
}
	.header .inner {
	    padding-top: 45px;
	    position: relative;
	    min-height: 72px;
	}

	/* Brand */
	header .brand, header .brand:hover {
	    float: left;
	    color: #fff;
	    display: inline-block;
	}
		.brand h1 {
		    margin: 0;
		    padding: 0;
		    font-size: 48px;
		    font-family: "HelveticaNeueLTPro-Bd", "Helvetica Neue LT Pro Bold", "HelveticaNeueBold", "HelveticaNeue-Bold", "Helvetica Neue Bold", "Helvetica Neue LT Pro", "HelveticaNeue", "Helvetica Neue", Helvetica, Arial, sans-serif;
		    color: #fff;
		    font-weight: 600;
		    font-stretch: normal; /* default value. No font stretching */
		    line-height: 1em;
		}
		.brand p {
		    color: #888;
		    margin-bottom: 22px;
		}

	/* Search form */

	.search-bar {
		position: absolute;
		right: 13px;
		top: 12px;
	}
		.search-bar .field {
			margin: 0;
			padding: 0;
		}
		.search-bar form input.text {
		    width: 155px;
		    padding: 5px 34px 5px 15px;
		    color: #888;
		    margin: 0;
		    border: none;
		    border-radius: 14px;
		    background: #fff;
		}

		.search-bar form input.action { /* positions the search button icon over the top of the search input */
		    font-size: 14px;
		    position: absolute;
		    right: 5px;
		    top: 0;
		    cursor: pointer;
		    border: none;
		    padding: 5px;
		    background: none;
		    font-family: 'WebSymbolsRegular';
		    color: #848484;
			border-radius: 0;
			margin: 0;
		}
		.search-bar form input.active,
		.search-bar form input.action:hover {
		    color: #000;
		}
		.search-bar form input:focus,
		.header textarea:focus {
		    outline: none; /* removes default browser outlining on focus */
		}
		.search-dropdown-icon {
		    display: none; /* hides search-dropdown-icon when site is at full width - media queries set it to display:block when at mobile/tablet width */
		}



/* NAVIGATION */

	/* Primary navigation */
	.header .inner .unit {
		position: relative; /* used to position the main navigation */
	}
	.header .primary ul {
		position:absolute;
		bottom:0;
		right:0; /* positions the main navigation */
	}
	.header .primary li {
	    float: left;
	    padding-bottom: 14px;
	    margin: 0 5px;
	    position: relative;
	    white-space: nowrap; /* forces text to never wrap onto a second line */
	}
	.header .primary li a {
	    color: #888;
	    font-size: 15px;
	    font-family: Helvetica, Arial, sans-serif;
	    padding: 6px 8px;
	    font-weight: bold;
	    display: block;
	}
	.header .primary li a:hover {
	    color: #fff;
	}
	.header .primary li.section a,
	.header .primary li.current a {
	    color: #fff;
	}
	.header .primary li.section:after,
	.header .primary li.current:after {
	    content: '}'; /* adds triangle beneath current nav item ('}' is renders as a triangle when WebSymbolsRegular is set as the font) */
	    display: block;
	    position: absolute;
	    left: 50%;
	    margin-left: -7px;
	    bottom: -7px;
	    font-family: 'WebSymbolsRegular';
	    font-size: 30px;
	    color: #fff;
	    text-align: center;
	    line-height: 20px;
	}

	/* Tablet Navigation */
	/* When navigation and logo overlap tablet-nav is initialized */
	.tablet-nav .brand p {
	    margin-bottom: 0;
	}
	.tablet-nav .header .inner {
	    padding-top: 20px;
	}
	.tablet-nav .header .primary ul {
	    float: left;
	    clear: both;
	    position: relative;
	    margin: 20px 0 0 -13px;
	    white-space: nowrap;
	    right: auto; /* resets the right property value that is set for the desktop site */
	}
		.tablet-nav .header .primary ul li {
		    white-space: nowrap;
		}
			.tablet-nav .header .primary ul li a { /* there is no hover on touch devices so no transition on hover is necessary */
			    -moz-transition: none;
			    -webkit-transition: none;
			    transition: none;
			}
	.tablet-nav .footer .right {
	    float: left;
	    width: 100%;
	}

	/* Secondary navigation */
	.main .secondary h3 {
	    font-size: 20px;
	    color: #AAA;
	    margin: 0 0 8px 0;
	    font-family: "CamboRegular", Georgia, "Times New Roman", Times, serif;
	    font-weight: normal;
	}
	.main .secondary {
	    border-bottom: 1px solid #e5e5e5;
	}
		.main .secondary ul {
		    padding: 0;
		    margin: 0;
		}
		.main .secondary li {
		    border-top: 1px solid #e5e5e5;
		    position: relative;
		    list-style-type: none;
		    margin-bottom: 0;
		}
			.main .secondary li .arrow {
			    color: #b80000;
			    padding-right: 5px;
			    display: block;
			    font-size: 15px;
			    line-height: 20px;
			    position: absolute;
			    left: 2px;
			    top: 7px;
			    -moz-transition: 0.2s; /* this transition moves the arrow from left:2px to left:6px */
			    -webkit-transition: 0.2s;
			    transition: 0.2s;
			}
		.main .secondary li a:hover .arrow {
		    left: 6px; /* this sets the final position for the arrow transition */
		}
		.main .secondary li a { /* side nav link styling */
		    padding: 10px 0;
		    display: block;
		    text-transform: uppercase;
		    letter-spacing: 2px;
		    font-size: 11px;
		    color: #333;
		    line-height: 17px;
		    border-bottom: none;
		    font-family: 'Lucida Sans', 'Lucida Grande', Arial, Helvetica, sans-serif;
		}
		.main .secondary li .text {
		    padding-left: 28px;
		    display: block;
		}
		.main .secondary li.current a.current {
			color: #b80000;
			background-color: #EDEDED;
		}
		.main .secondary li.section,
		.main .secondary li.current {
		    background-color: #F3F3F3;
		}
			.main .secondary li.section a,
			.main .secondary li.current a {
			    color: #000;
			}


		/* Secondary navigation 2-5 levels deep */
		.main .secondary ul ul {
			display: none;
		}
		.secondary ul li.current ul,
		.secondary ul li.section ul { /* Only show child pages from selected parent */
			display: block;
		}
		.secondary li.current ul ul {
			display: none;
		}
		.main .secondary ul ul li a { padding-left: 10px; } /* Indent all sidebar navigation levels*/
			.main .secondary ul ul li a .arrow { left: 12px; }
			.main .secondary ul ul li a:hover .arrow { left: 16px; }

		.main .secondary ul ul ul li a { padding-left: 20px; }
			.main .secondary ul ul ul li a .arrow { left: 22px; }
			.main .secondary ul ul ul li a:hover .arrow { left: 26px; }

		.main .secondary ul ul ul ul li a { padding-left: 30px; }
			.main .secondary ul ul ul ul li a .arrow { left: 32px; }
			.main .secondary ul ul ul ul li a:hover .arrow { left: 36px; }

		.main .secondary ul ul ul ul ul li a { padding-left: 40px; }
			.main .secondary ul ul ul ul ul li a .arrow { left: 32px; }
			.main .secondary ul ul ul ul ul li a:hover .arrow { left: 36px; }

		.main .secondary li a:hover,
		.main .secondary li.section a:hover,
		.main .secondary li.current a:hover {
			color: #b80000;
		}




/* MIXED */
header:after,
.main:after,
#Root:after,
.search-bar:after,
header .inner:after,
footer:after { /* clearfix */
    height: 0;
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
}
.search-bar form input.action,
.header .primary li a,
.footer a { /* adds color transition when links/inputs on hover */
    -moz-transition: color 0.2s;
    -webkit-transition: color 0.2s;
    transition: color 0.2s;
}
.footer a.brand { color: #333; margin-left: 0; }
.footer a.brand:hover { color: #B80000; }
body h1 span.amp {
    font-family: Baskerville,"Goudy Old Style","Palatino","Book Antiqua",Georgia;
    font-style: italic;
}


/* FOOTER */
.footer {
    color: #6B6B6B;
    background: #ededed;
    padding: 20px 0;
    font-size: 11px;
    line-height: 22px;
}
	.footer a {
	    color: #6B6B6B;
	}
	.footer a:hover {
	    color: #B90000;
	}
	.footer .left {
	    float: left;
	    color: #000;
	    display: block;
	    margin-bottom: 10px;
	}
	.footer .right {
	    float: right;
	    display: block;
	    margin-bottom: 10px;
	}
	.footer span {
	    padding: 0 3px;
	    color: #bbb;
	}
	.footer .primary,
	.footer .primary ul {
	    display: inline;
	    margin: 0;
	    padding: 0;
	}
		.footer .primary li {
		    display: inline;
		}
	.ie6 .footer .primary li,
	.ie7 .footer .primary li { /* this is a bugfix for ie6/7 */
	    display: inline;
	    zoom: 1;
	    margin-right: 10px;
	}
	.footer .primary li:after { /* adds '/' to separate the footer navigation items */
	    padding: 0 3px 0 5px;
	    content: '/';
	    color: #999;
	}
	.footer .primary li:last-child:after {
	    content: ''; /* makes sure last nav item doesn't have a '/' following it */
	}
	.footer .arrow {
	    padding: 0 8px 0 5px;
	    color: #b80000;
	    font-size: 13px;
	}
	.footer .primary .nav-open-button {
	    display: none; /* the footer includes the primary nav include - this makes sure the nav open close button doesn't show up */
	}

/* PAGE SPECIFIC LAYOUT */

	/* Homepage */
		/* currently no Hompage specific styles - feel free to add your own */

	/* Search Results */
	.typography .searchResults h1 {
		margin-bottom: 0;
		padding-bottom: 0;
		border-bottom: none;
	}
	.searchResults p.searchQuery {
	    margin-bottom: 10px;
	    font-size: 15px;
	    font-weight: bold;
	}
	.searchResults ul#SearchResults {
	    padding: 0;
	    border-bottom: 1px solid #e5e5e5;
	    margin:0;
	}
		.searchResults ul#SearchResults li {
		    border-top: 1px solid #e5e5e5;
		    padding: 20px 0;
		    list-style-type: none;
		}
		.searchResults ul#SearchResults p {
		    margin-bottom: 10px;
		}
		.searchResults #PageNumbers a {
		    padding: 0 5px;
		}
		.searchResults #PageNumbers .pagination {
		   	border-bottom: 1px solid #e5e5e5;
		    padding: 20px 0;
		    display:table; /* displays the pagination as a table so that elements stay inline and the middle column adjusts its size to accomodate and the right arrow stays to the right */
		    width:100%;
		}
		.searchResults #PageNumbers .pagination button{
			display:table-cell; /* each element in the pagination div displays as a table cell */
		}
		.searchResults #PageNumbers p {
		    text-align: center;
		    padding:20px 0;
		}
		.searchResults #PageNumbers .next,
		.searchResults #PageNumbers .prev {
		    font-size: 14px;
		    padding: 0 20px;
		    display:table-cell; /* each element in the pagination div displays as a table cell */
		    vertical-align: middle;
		    border-bottom:0 !important;
		}
		.searchResults #PageNumbers .next {
		    margin-left: 15px;
		}
		.searchResults #PageNumbers .prev {
		    margin-right: 15px;
		}

/* DEVICE & RESPONSIVE LAYOUT */
.header .nav-open-button {
    display: none; /* removes the nav toggle button for desktop site */
}
#media-query-trigger {
    /* instead of detecting the width of the window in simple/javascript/script.js it detects the visibility of this element (which is set using media queries)
    instead to trigger the hiding/showing of nav and search in mobile mode */
    display: none;
    visibility: hidden;
}

/* BREAKPOINT 960px */

@media only screen and (max-width: 960px) {
	.content img {
	    max-width: 97%;
	    height: auto;
	}
	.header .primary ul {
	    margin-left: -12px;
	    -webkit-padding-start: 0px; /* removes default webkit padding on ul items */
	}
}

/* BREAKPOINT 640px */

/* when changing the breakpoint below, change it ito the same value in the script.js file as well */
@media only screen and (max-width: 640px) {
	body {
	    max-width: 640px;
	}
	#media-query-trigger {
	    visibility: visible;
	}

	/* Navigation*/

	.tablet-nav .header .brand {
	    float: none;
	    display: inline-block;
	    margin-left: 22px;
	    margin-bottom: 22px;
	}
		.brand h1 {
		    font-size: 40px;
		}
		.brand h1 {
		    padding-right: 100px; /* padding stops .brand text from overlapping the search and nav buttons */
		}
	.tablet-nav .header {
	    padding: 0;
	}
		.tablet-nav .header .inner {
		    padding: 20px 0 0 0;
		    min-height: 0;
		}
		.tablet-nav .header .primary .nav-open-button { /* styling and positioning of the nav toggle button */
		    z-index: 100;
		    width: 20px;
		    height: 20px;
		    position: absolute;
		    right: 20px;
		    top: 35px;
		    display: block;
		    cursor: pointer;
		    font-family: 'WebSymbolsRegular';
		    font-size: 20px;
		    color: #ededed;
            background: #000;
            margin-top: 0;
            padding: 0;
		}
		.tablet-nav .header .primary ul {
		    z-index: 10;
		    position: relative;
		    display: none; /* initially hiding the navigation */
		    float: left;
		    margin: 0;
		    padding: 0;
		    white-space: normal;
		    width: 100%;
		}
			.tablet-nav .header .primary ul li {
			    width: 100%;
			    margin: 0;
			    padding: 0;
			    float: none; /* displays list items vertically */
			    background: none;
			    position: relative;
			    text-shadow: 0 1px #fff;
			}
			.tablet-nav .header .primary ul li:after { /* creates the arrow for the primary nav links */
			    content: '\003e';
			    display: block;
			    position: absolute;
			    right: 20px;
			    top: 0px;
                font-family: 'WebSymbolsRegular';
			    font-size: 14px;
			    color: #999;
			    text-align: center;
			    vertical-align: middle;
			    line-height: 38px;
			}
			.tablet-nav .header .primary ul li a,
			.tablet-nav .header .primary ul li.current a,
			.tablet-nav .header .primary ul li.section a { /* styling the  top level nav links */
			    padding: 10px 0 10px 22px;
			    font-weight: bold;
			    border-bottom: 1px solid #bbb;
			    color: #434343;
			    background: #e7e7e7;
			}
			.tablet-nav .header .primary ul li.current a,
			.tablet-nav .header .primary ul li.section a {
				background: #CCCCCC; /* makes background on current top level page slightly darker */
			}
			.tablet-nav .header .primary ul li a:hover {
			    color: inherit;
			}
			.tablet-nav .header .primary li.section:after,
			.tablet-nav .header .primary li.current:after {
			    display: none; /* hides the link arrow on current top level page */
			}
			.tablet-nav .tablet-nav .header nav.primary ul li {
				padding: 0;
			}

	/* Search Form */
	.search-bar { /* adds new styling to mobile search bar */
	    width: 100%;
	    position: relative;
	    top: 0;
	    right: 0;
	    display: none; /* hides searchbar initially */
	    padding: 20px 0;
	    margin: 0;
	    background-color: #E7E7E7;
	}
	.search-dropdown-icon { /* styling for search toggle button */
	    display: block;
	    cursor: pointer;
	    width: 20px;
	    height: 20px;
	    position: absolute;
	    right: 60px;
	    top: 34px;
	    font-family: 'WebSymbolsRegular';
	    font-size: 20px;
	    color: #ededed;
	    text-align: center;
	    line-height: 20px;
	}
	.search-bar form {
	    margin: 0;
	    width: 100%;
	}
		.search-bar form fieldset {
		    padding: 0 18px;
		    left: 0;
		    right: 0;
		    position: relative;
		}
	.search-bar div.field {
	    margin-bottom: 0;
	}
	.search-bar form input.text {
	    width: 89%; /* makes search input full width - allowing for space either side */
	    max-width: 89%;
	    padding: 8px 10% 8px 1%;
	    text-indent: 15px;
	    position: relative;
	    display: block;
	    right: 0;
	    left: 0;
	    border: 1px solid #e5e5e5;
	    background: #fff;
	    font-size: 17px;
	    border-radius: 20px; /* increase border radius due to increased padding */
	}
	.search-bar form input.action {
	    right: 5%;
	    top: 2px;
	    font-size: 18px;
	}

	/* Main Content */
	.main {
	    padding: 20px 0 45px; /* decrease padding so that more content can fit on screen */
	}
	.content-container,
	.sidebar {
	    width: 100%; /* sidenav is now shown above the page content */
	    margin-bottom: 30px;
	}
		.typography h1 { /* decrease size of page heading due to smaller screen */
		    font-size: 30px;
			line-height:35px;
		    margin-bottom: 15px;
		    padding-bottom: 10px;
		}
		.typography p {
		    font-size: 14px;
		    line-height: 23px;
		}
	p.intro {
	    font-size: 19px;
	    line-height: 27px;
	}
	.main .inner {
	    padding: 0 22px;
	}
	/* Secondry Nav */
	.secondary li a {
	    line-height: 24px;
	}
	.secondary li .arrow {
	    line-height: 26px;
	}
	/* Footer */
	.footer .right {
	    float: left;
	    width: 100%;
	}
}

/* Print Styles */

/* Based on HTML5 boilerplate print styles */
@media print {
	* {
	    background: transparent !important;
	    color: black !important;
	    box-shadow: none !important;
	    text-shadow: none !important;
	    filter: none !important;
	    -ms-filter: none !important;
	}
	a,
	a:visited {
	    text-decoration: underline
	}
	a[href]:after {
        content: " (" attr(href) ")";
    }
    abbr[title]:after {
        content: " (" attr(title) ")";
    }

    /*
     * Don't show links for images, or javascript/internal links
     */

    .ir a:after,
    a[href^="javascript:"]:after,
    a[href^="#"]:after {
        content: "";
    }

	thead {
	    display: table-header-group
	}
	tr,
	img {
	    page-break-inside: avoid
	}
	img {
	    max-width: 100% !important
	}
	pre,
    blockquote {
        border: 1px solid #999;
        page-break-inside: avoid;
    }
    @page {
        margin: 0.5cm;
    }
    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3;
    }

    h2,
    h3 {
        page-break-after: avoid;
    }

    /* Simple theme custom print styles */
	.header,
	.footer,
    .nav-open-button,
    .search-bar,
    .search-dropdown-icon,
    nav.primary {
	    display: none;
	}
}
