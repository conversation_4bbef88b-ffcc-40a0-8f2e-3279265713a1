<?php

 /** GENERATED CODE -- DO NOT MODIFY **/

namespace SSGraphQLSchema_21232f297a57a5a743894a0e4a801fc3;
use GraphQL\Type\Definition\EnumType;
// @type:AppCategory
class A00549ce6d22422f8dbb362a440b6cded extends EnumType
{
    public function __construct()
    {
        parent::__construct([
            'name' => 'AppCategory',
            'values' => [
                        'ARCHIVE' => [
                    'value' => array (
  0 => 'ace',
  1 => 'arc',
  2 => 'arj',
  3 => 'bz',
  4 => 'bz2',
  5 => 'cab',
  6 => 'dmg',
  7 => 'gz',
  8 => 'hqx',
  9 => 'jar',
  10 => 'rar',
  11 => 'sit',
  12 => 'sitx',
  13 => 'tar',
  14 => 'tgz',
  15 => 'zip',
  16 => 'zipx',
),
                                ],
                        'AUDIO' => [
                    'value' => array (
  0 => 'aif',
  1 => 'aifc',
  2 => 'aiff',
  3 => 'apl',
  4 => 'au',
  5 => 'avr',
  6 => 'cda',
  7 => 'm4a',
  8 => 'mid',
  9 => 'midi',
  10 => 'mp3',
  11 => 'ogg',
  12 => 'ra',
  13 => 'ram',
  14 => 'rm',
  15 => 'snd',
  16 => 'wav',
  17 => 'wma',
),
                                ],
                        'DOCUMENT' => [
                    'value' => array (
  0 => 'brf',
  1 => 'css',
  2 => 'csv',
  3 => 'doc',
  4 => 'docx',
  5 => 'dotm',
  6 => 'dotx',
  7 => 'htm',
  8 => 'html',
  9 => 'js',
  10 => 'kml',
  11 => 'pages',
  12 => 'pdf',
  13 => 'potm',
  14 => 'potx',
  15 => 'pps',
  16 => 'ppt',
  17 => 'pptx',
  18 => 'rtf',
  19 => 'txt',
  20 => 'xhtml',
  21 => 'xls',
  22 => 'xlsx',
  23 => 'xltm',
  24 => 'xltx',
  25 => 'xml',
  26 => 'graphql',
),
                                ],
                        'IMAGE' => [
                    'value' => array (
  0 => 'alpha',
  1 => 'als',
  2 => 'bmp',
  3 => 'cel',
  4 => 'gif',
  5 => 'ico',
  6 => 'icon',
  7 => 'jpeg',
  8 => 'jpg',
  9 => 'pcx',
  10 => 'png',
  11 => 'ps',
  12 => 'psd',
  13 => 'tif',
  14 => 'tiff',
  15 => 'webp',
),
                                ],
                        'IMAGESUPPORTED' => [
                    'value' => array (
  0 => 'gif',
  1 => 'jpeg',
  2 => 'jpg',
  3 => 'png',
  4 => 'bmp',
  5 => 'ico',
  6 => 'webp',
),
                                ],
                        'FLASH' => [
                    'value' => array (
  0 => 'fla',
  1 => 'swf',
),
                                ],
                        'VIDEO' => [
                    'value' => array (
  0 => 'asf',
  1 => 'avi',
  2 => 'flv',
  3 => 'ifo',
  4 => 'm1v',
  5 => 'm2v',
  6 => 'm4v',
  7 => 'mkv',
  8 => 'mov',
  9 => 'mp2',
  10 => 'mp4',
  11 => 'mpa',
  12 => 'mpe',
  13 => 'mpeg',
  14 => 'mpg',
  15 => 'ogv',
  16 => 'qt',
  17 => 'vob',
  18 => 'webm',
  19 => 'wmv',
),
                                ],
                    ],
                ]);
    }
}
