#!/bin/bash

echo "Creating admin user..."

# Method 1: Try using the CreateDefaultAdmin task
docker-compose exec web php public/index.php dev/tasks/CreateDefaultAdmin username=admin password=admin123

# Method 2: Alternative approach using MemberCreateTask if available
echo "Attempting alternative admin creation method..."
docker-compose exec web php public/index.php dev/tasks/MemberCreateTask username=admin password=admin123 email=<EMAIL>

# Method 3: Manual SQL approach as fallback
echo "Creating admin via direct database method..."
docker-compose exec web php -r "
require_once 'vendor/autoload.php';
use SilverStripe\Security\Member;
use SilverStripe\Security\Group;
use SilverStripe\Core\Environment;
use SilverStripe\ORM\DB;

// Initialize SilverStripe
\$_SERVER['HTTP_HOST'] = 'localhost:8080';
\$_SERVER['REQUEST_URI'] = '/';
require_once 'public/index.php';

// Create admin member
\$admin = Member::get()->filter('Email', '<EMAIL>')->first();
if (!\$admin) {
    \$admin = Member::create();
    \$admin->FirstName = 'Admin';
    \$admin->Surname = 'User';
    \$admin->Email = '<EMAIL>';
    \$admin->setPassword('admin123');
    \$admin->write();
    
    // Add to administrators group
    \$adminGroup = Group::get()->filter('Code', 'administrators')->first();
    if (\$adminGroup) {
        \$admin->Groups()->add(\$adminGroup);
    }
    
    echo 'Admin user created successfully';
} else {
    echo 'Admin user already exists';
}
"

echo "Admin creation complete!"
echo "Try logging in with: <EMAIL> / admin123"
