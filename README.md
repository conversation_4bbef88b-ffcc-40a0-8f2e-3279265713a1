# SilverStripe Docker Setup

A simple Docker Compose setup for SilverStripe development.

## What's Included

- **Web Server**: PHP 8.1 with Apache
- **Database**: MySQL 8.0
- **PHPMyAdmin**: Database management interface
- **SilverStripe**: Latest stable version

## Quick Start

1. **Start the containers:**
   ```bash
   docker-compose up -d
   ```

2. **Install SilverStripe:**
   ```bash
   ./install-silverstripe.sh
   ```

3. **Access your site:**
   - Website: http://localhost:8080
   - Admin Panel: http://localhost:8080/admin
   - PHPMyAdmin: http://localhost:8081

## Default Credentials

- **SilverStripe Admin:**
  - Username: `admin`
  - Password: `admin123`

- **Database:**
  - Host: `db`
  - Database: `silverstripe`
  - Username: `silverstripe`
  - Password: `silverstripe`

- **PHPMyAdmin:**
  - Username: `silverstripe`
  - Password: `silverstripe`

## Directory Structure

```
.
├── docker-compose.yml          # Docker Compose configuration
├── Dockerfile                  # Custom PHP/Apache image
├── .env                       # Environment variables
├── install-silverstripe.sh    # Installation script
├── app/                       # SilverStripe application files
├── docker/
│   ├── php/
│   │   └── php.ini           # PHP configuration
│   └── apache/
│       └── 000-default.conf  # Apache virtual host
└── README.md
```

## Useful Commands

- **Start containers:** `docker-compose up -d`
- **Stop containers:** `docker-compose down`
- **View logs:** `docker-compose logs -f`
- **Access web container:** `docker-compose exec web bash`
- **Access database container:** `docker-compose exec db mysql -u silverstripe -p`
- **Run SilverStripe tasks:** `docker-compose exec web php public/index.php dev/tasks`

## Development

- Your SilverStripe code will be in the `app/` directory
- Changes to PHP files will be reflected immediately
- Database data is persisted in a Docker volume

## Troubleshooting

- If you get permission errors, run: `docker-compose exec web chown -R www-data:www-data /var/www/html`
- To reset the database, run: `docker-compose down -v` then `docker-compose up -d`
- Check logs with: `docker-compose logs web` or `docker-compose logs db`
