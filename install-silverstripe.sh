#!/bin/bash

# Create SilverStripe project if it doesn't exist
if [ ! -f "app/composer.json" ]; then
    echo "Installing SilverStripe..."
    docker-compose exec web composer create-project silverstripe/installer . --no-dev
    echo "SilverStripe installation complete!"
else
    echo "SilverStripe already installed. Running composer install..."
    docker-compose exec web composer install --no-dev
fi

# Set proper permissions
docker-compose exec web chown -R www-data:www-data /var/www/html
docker-compose exec web chmod -R 755 /var/www/html

echo "Setup complete! Visit http://localhost:8080 to see your SilverStripe site."
echo "Admin panel: http://localhost:8080/admin"
echo "Default admin credentials: admin / admin123"
echo "PHPMyAdmin: http://localhost:8081"
